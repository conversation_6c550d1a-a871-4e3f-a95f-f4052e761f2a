'use client';

import React, { useEffect, useContext } from 'react';
import { useRouter } from 'next/navigation';
import { useUser } from "@clerk/nextjs";
import { UserDetailContext } from "@/context/UserDetailContext";
import AIVideoCreationContainer from '../_components/AIVideoCreationContainer';

/**
 * AI Video Creation Client Component
 *
 * This component contains all the interactive client-side logic for the AI Video
 * creation page, including authentication checks, routing, and the main container.
 *
 * It receives initial credits from the server component for better performance
 * while maintaining all existing functionality and custom hooks.
 */
export default function AIVideoCreationClient({ initialCredits }) {
  const { isLoaded, isSignedIn } = useUser();
  const router = useRouter();
  const { userDetail } = useContext(UserDetailContext);

  // Redirect unauthenticated users to the sign-in page
  useEffect(() => {
    if (isLoaded && !isSignedIn) {
      router.push('/sign-in');
    }
  }, [isLoaded, isSignedIn, router]);

  // Show loading state while checking authentication
  if (!isLoaded) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // Only render the page content if the user is signed in
  if (isSignedIn) {
    return <AIVideoCreationContainer initialCredits={initialCredits} />;
  }

  // If not signed in and not loading, the useEffect will handle the redirect
  return null;
}
