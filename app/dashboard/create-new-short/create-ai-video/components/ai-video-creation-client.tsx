'use client';

import React, { useEffect, useContext, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useUser } from "@clerk/nextjs";
import { UserDetailContext } from "@/context/UserDetailContext";
import { toast } from 'sonner';

// Import atomic design organisms
import {
  VideoCreationLayout,
  AIVideoConfigurationForm,
  AIVideoPreviewPanel
} from '@/components/organisms';

// Import existing hooks (adapted for atomic components)
import { useAIVideoForm } from '../hooks/useAIVideoForm';
import { useVideoGeneration } from '../hooks/useVideoGeneration';
import { generateAIVideoScript } from '@/actions/aiVideoActions';

/**
 * AI Video Creation Client Component
 *
 * Migrated to use atomic design organisms while maintaining all existing functionality.
 * Uses VideoCreationLayout with AIVideoConfigurationForm and AIVideoPreviewPanel.
 */
export default function AIVideoCreationClient({ initialCredits }) {
  const { isLoaded, isSignedIn } = useUser();
  const router = useRouter();
  const { userDetail } = useContext(UserDetailContext);

  // Form state management (existing hook)
  const {
    formData,
    errors,
    touched,
    isValid,
    hasErrors,
    estimatedDuration,
    updateField,
    updateFields,
    validateForm,
    handleFieldBlur,
    getFieldError,
    resetForm,
    getSubmissionData
  } = useAIVideoForm();

  // Video generation management (existing hook)
  const {
    isGenerating,
    generationMessage,
    previewScript,
    generatePreview,
    generateVideo,
    clearPreview,
    isGenerateButtonDisabled,
    getGenerationButtonText,
    getGenerationButtonTitle,
    hasSufficientCredits,
    VIDEO_GENERATION_COST
  } = useVideoGeneration();

  // Local state for script generation
  const [isGeneratingScript, setIsGeneratingScript] = useState(false);

  // Redirect unauthenticated users to the sign-in page
  useEffect(() => {
    if (isLoaded && !isSignedIn) {
      router.push('/sign-in');
    }
  }, [isLoaded, isSignedIn, router]);

  // Enhanced script generation handler for atomic components
  const handleGenerateScript = async () => {
    if (!formData.topic?.trim()) {
      toast.error('Please enter a topic first');
      return;
    }

    if (!formData.videoStyle) {
      toast.error('Please select a video style first');
      return;
    }

    setIsGeneratingScript(true);

    try {
      const result = await generateAIVideoScript({
        topic: formData.topic,
        videoStyle: formData.videoStyle
      });

      if (result.success && result.script) {
        updateField('script', result.script);
        toast.success('Script generated successfully!');
      } else {
        throw new Error(result.error || 'Failed to generate script');
      }
    } catch (error) {
      console.error('Script generation error:', error);
      toast.error('Failed to generate script. Please try again.');
    } finally {
      setIsGeneratingScript(false);
    }
  };

  // Enhanced video generation with validation
  const handleGenerateVideo = async () => {
    // Validate form before submission
    if (!validateForm()) {
      toast.error('Please complete all required fields');
      return false;
    }

    // Get clean submission data
    const submissionData = getSubmissionData();
    return await generateVideo(submissionData);
  };

  // Show loading state while checking authentication
  if (!isLoaded) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // Only render the page content if the user is signed in
  if (!isSignedIn) {
    return null;
  }

  // Prepare configuration data for atomic components
  const configurationData = {
    projectTitle: formData.projectTitle,
    topic: formData.topic,
    videoStyle: formData.videoStyle,
    aspectRatio: formData.aspectRatio,
    script: formData.script,
    voice: formData.voice,
    audioSpeed: formData.audioSpeed,
    backgroundMusic: formData.backgroundMusic,
    caption: formData.caption,
    templateId: formData.templateId,
    scriptMode: formData.script ? 'manual' : 'ai',
    aiPrompt: formData.topic,
    manualScript: formData.script,
    generatedScript: previewScript,
    isGeneratingScript
  };

  // Header content
  const headerContent = (
    <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-50 via-purple-50 to-blue-50 dark:from-blue-950/20 dark:via-purple-950/20 dark:to-blue-950/20 p-8 border border-border/50">
      <div className="relative z-10">
        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
          <div className="space-y-3">
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              AI Video Creator ✨
            </h1>
            <p className="text-lg text-muted-foreground max-w-2xl">
              Create engaging videos with AI-generated scripts, professional voiceovers, and dynamic visuals.
              Perfect for social media, marketing, and educational content.
            </p>
          </div>
          <div className="flex items-center gap-4">
            <div className="text-right">
              <p className="text-sm text-muted-foreground">Credits Available</p>
              <p className="text-2xl font-bold text-primary">
                {userDetail?.credits || initialCredits || 0}
              </p>
            </div>
          </div>
        </div>
      </div>
      {/* Background decoration */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-2xl"></div>
    </div>
  );

  return (
    <VideoCreationLayout
      header={headerContent}
      configurationPanel={
        <AIVideoConfigurationForm
          formData={configurationData}
          errors={errors}
          onFieldChange={updateField}
          onFieldsChange={updateFields}
          sectionProps={{
            project: {
              title: "AI Video Project",
              description: "Set up your AI-generated video project"
            },
            script: {
              title: "Video Script",
              description: "Create your video script using AI or write it manually",
              onGenerateScript: handleGenerateScript,
              isGeneratingScript
            },
            aspectRatio: {
              title: "Video Format",
              description: "Choose the aspect ratio for your AI video"
            }
          }}
        />
      }
      previewPanel={
        <AIVideoPreviewPanel
          configuration={configurationData}
          credits={userDetail?.credits || initialCredits || 0}
          onGenerate={handleGenerateVideo}
          isGenerating={isGenerating}
          status={isGenerating ? 'processing' : 'pending'}
          canGenerate={isValid && hasSufficientCredits()}
          generationError={hasErrors ? 'Please fix form errors before generating' : null}
        />
      }
    />
  );
}
