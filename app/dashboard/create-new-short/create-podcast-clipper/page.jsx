"use client"

import React, { useState, useEffect, useContext } from 'react';
import { useUser } from "@clerk/nextjs";
import { useRouter } from 'next/navigation';
import { v4 as uuidv4 } from 'uuid';
import { toast } from 'sonner';
import { UserDetailContext } from "@/context/UserDetailContext";
import { VIDEO_GENERATION_COST } from '@/lib/creditUtils';

// Import atomic design organisms
import {
  VideoCreationLayout,
  PodcastClipperConfigurationForm,
  PodcastClipperPreviewPanel
} from '@/components/organisms';

// Server Actions
import { processVideo } from '@/actions/generation';



// --- Main Component ---

const PodcastClipperPage = () => {
  // --- Authentication ---
  const { user, isSignedIn } = useUser();
  const { userDetail, isLoaded } = useContext(UserDetailContext);
  const router = useRouter();

  // Consolidated form state using atomic design patterns
  const [formData, setFormData] = useState({
    projectTitle: '',
    youtubeLink: '',
    uploadedFiles: [],
    userPrompt: '',
    numberOfClips: 1,
    clipDuration: 60,
    audioQuality: 'high',
    includeTranscription: true
  });

  const [errors, setErrors] = useState({});
  const [isClipping, setIsClipping] = useState(false);
  const [isFileUploading, setIsFileUploading] = useState(false);

  // Field update handler for atomic components
  const updateField = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when field is updated
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };


  // Form validation
  const validateForm = () => {
    const newErrors = {};

    if (!formData.projectTitle?.trim()) {
      newErrors.projectTitle = "Project title is required";
    }

    if (!formData.uploadedFiles?.length && !formData.youtubeLink?.trim()) {
      newErrors.uploadedFiles = "Please upload a file or provide a YouTube link";
    }

    if (!formData.userPrompt?.trim()) {
      newErrors.userPrompt = "Please describe what type of clips you want";
    }

    if (!formData.numberOfClips || formData.numberOfClips < 1) {
      newErrors.numberOfClips = "Number of clips must be at least 1";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };


  // Handle podcast clipping with atomic design patterns
  const handleClipPodcast = async () => {
    console.log("handleClipPodcast: Button clicked");

    // Validate form
    if (!validateForm()) {
      toast.error("Please complete all required fields");
      return;
    }

    // Credit check
    if (!userDetail || userDetail.credits < VIDEO_GENERATION_COST) {
      toast.error("Insufficient Credits", {
        description: `Need ${VIDEO_GENERATION_COST}, have ${userDetail?.credits || 0}.`
      });
      return;
    }

    if (!user?.id) {
      toast.error("Please log in to continue.");
      return;
    }

    setIsClipping(true);

    try {
      let sourceData = null;

      if (formData.youtubeLink?.trim()) {
        sourceData = { type: 'youtube', url: formData.youtubeLink };
      } else if (formData.uploadedFiles?.length) {
        const file = formData.uploadedFiles[0];
        sourceData = {
          type: 'upload',
          s3Key: file.s3Key,
          uploadedFileId: file.uploadedFileId,
          fileName: file.fileName,
        };
      }

      if (!sourceData) {
        toast.error("Please provide a YouTube link or upload a file.");
        setIsClipping(false);
        return;
      }

      console.log('Triggering Inngest event for podcast clipping...');

      const requestId = uuidv4();
      const { success, error: inngestError, eventId } = await processVideo({
        source: sourceData,
        userPrompt: formData.userPrompt,
        numberOfClips: formData.numberOfClips,
        userId: user.id,
        requestId: requestId,
      });

      if (!success) {
        console.error('Failed to send Inngest event:', inngestError);
        toast.error(`Failed to start clipping: ${inngestError}`);
        setIsClipping(false);
      } else {
        console.log('Inngest event sent successfully with ID:', eventId);
        toast.success('Clipping process started in the background!');
        router.push('/dashboard');
      }

    } catch (err) {
      console.error('Error triggering Inngest event:', err);
      const errorMessage = err.message || 'An unexpected error occurred while starting the clipping process';
      toast.error(`An error occurred: ${errorMessage}`);
      setIsClipping(false);
    }
  };

  // Check if form is valid for generation
  const canGenerate = formData.projectTitle?.trim() &&
                     (formData.uploadedFiles?.length || formData.youtubeLink?.trim()) &&
                     formData.userPrompt?.trim() &&
                     formData.numberOfClips >= 1 &&
                     userDetail?.credits >= VIDEO_GENERATION_COST;


  // --- Derived State / Variables ---
  // Enhanced validation with credit checking
  const isGenerateButtonDisabled = (!youtubeLink && !uploadedFileDetails) ||
                                   isFileUploading ||
                                   isClipping ||
                                   !userDetail ||
                                   userDetail.credits < VIDEO_GENERATION_COST;

  // Determine disabled state for individual inputs
  const isYoutubeInputDisabled = !!uploadedFileDetails || isFileUploading || isClipping;
  const isFileUploadDisabled = !!youtubeLink || !!uploadedFileDetails || isFileUploading || isClipping;
  const isPromptInputDisabled = isFileUploading || isClipping;
  const isNumberOfClipsInputDisabled = isFileUploading || isClipping;

  // Removed console.log as per instruction (assuming the instruction was the log output itself)


  // --- Authentication Check ---
  useEffect(() => {
    if (isLoaded && !isSignedIn) {
      router.push('/sign-in');
    }
  }, [isLoaded, isSignedIn, router]);

  // --- Loading State ---
  if (!isLoaded) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // --- Unauthenticated State ---
  if (!isSignedIn) {
    return null;
  }

  // Header content
  const headerContent = (
    <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-green-50 via-blue-50 to-green-50 dark:from-green-950/20 dark:via-blue-950/20 dark:to-green-950/20 p-8 border border-border/50">
      <div className="relative z-10">
        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
          <div className="space-y-3">
            <h1 className="text-4xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
              Podcast Clipper ✂️
            </h1>
            <p className="text-lg text-muted-foreground max-w-2xl">
              Transform your long-form podcast content into engaging short clips perfect for social media.
              Upload your audio/video file or provide a YouTube link to get started.
            </p>
          </div>
          <div className="flex items-center gap-4">
            <div className="text-right">
              <p className="text-sm text-muted-foreground">Credits Available</p>
              <p className="text-2xl font-bold text-primary">
                {userDetail?.credits || 0}
              </p>
            </div>
          </div>
        </div>
      </div>
      {/* Background decoration */}
      <div className="absolute inset-0 bg-gradient-to-br from-green-500/10 to-blue-500/10 rounded-2xl"></div>
    </div>
  );

  return (
    <VideoCreationLayout
      header={headerContent}
      configurationPanel={
        <PodcastClipperConfigurationForm
          formData={formData}
          errors={errors}
          onFieldChange={updateField}
          sectionProps={{
            project: {
              title: "Podcast Clipper Project",
              description: "Set up your podcast clipping project"
            },
            media: {
              title: "Upload Podcast",
              description: "Upload your podcast audio file for clipping",
              acceptedTypes: ['audio/*', 'video/*'],
              multiple: false,
              maxFiles: 1
            }
          }}
        />
      }
      previewPanel={
        <PodcastClipperPreviewPanel
          configuration={formData}
          credits={userDetail?.credits || 0}
          onGenerate={handleClipPodcast}
          isGenerating={isClipping}
          status={isClipping ? 'processing' : 'pending'}
          canGenerate={canGenerate}
          generationError={Object.keys(errors).length > 0 ? 'Please fix form errors before generating' : null}
        />
      }
    />
  );


};

export default PodcastClipperPage;
