
"use client";

import React, { useState, useContext, useEffect } from 'react';
import { useUser } from "@clerk/nextjs";
import { useRouter } from 'next/navigation';
import { toast } from "sonner";

// Import atomic design organisms
import {
  VideoCreationLayout,
  MemeVideoConfigurationForm,
  MemeVideoPreviewPanel
} from '@/components/organisms';

// Import context
import { UserDetailContext } from "@/context/UserDetailContext";

// Import server action
import { triggerMemeVideoGeneration } from "@/actions/memeVideoGeneration";

// Import constants
import { VIDEO_GENERATION_COST } from '@/lib/creditUtils';

function CreateMemeVideoPage() {
  const { isLoaded, isSignedIn } = useUser();
  const router = useRouter();
  const { userDetail, setUserDetail } = useContext(UserDetailContext);

  // Redirect unauthenticated users to the sign-in page
  useEffect(() => {
    if (isLoaded && !isSignedIn) {
      router.push('/sign-in');
    }
  }, [isLoaded, isSignedIn, router]);

  // Consolidated form state using atomic design patterns
  const [formData, setFormData] = useState({
    projectTitle: "",
    videoSource: null,
    memeText: "",
    font: "Impact",
    fontSize: 48,
    textColor: "#FFFFFF",
    textOutline: true,
    outlineColor: "#000000",
    outlineThickness: 2,
    textShadow: false,
    backgroundColor: null,
    textPosition: "bottom-center",
    aspectRatio: "9:16",
    videoStartTime: 0,
    videoEndTime: null,
    useOriginalAudio: true,
    backgroundMusic: null,
    originalAudioVolume: 1.0,
    backgroundMusicVolume: 0.5,
    uploadedFiles: []
  });

  const [errors, setErrors] = useState({});
  const [isGenerating, setIsGenerating] = useState(false);

  // Field update handler for atomic components
  const updateField = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when field is updated
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };


  // Form validation
  const validateForm = () => {
    const newErrors = {};

    if (!formData.projectTitle?.trim()) {
      newErrors.projectTitle = "Project title is required";
    }

    if (!formData.uploadedFiles?.length && !formData.videoSource) {
      newErrors.uploadedFiles = "Please upload a video file";
    }

    if (!formData.memeText?.trim()) {
      newErrors.memeText = "Meme text is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle video generation with atomic design patterns
  const handleGenerateVideo = async () => {
    console.log("Initiating Meme video generation via server action...");

    // Validate form
    if (!validateForm()) {
      toast.error("Please complete all required fields");
      return;
    }

    // Credit check
    if (!userDetail || userDetail.credits < VIDEO_GENERATION_COST) {
      toast.error("Insufficient Credits", {
        description: `Need ${VIDEO_GENERATION_COST}, have ${userDetail?.credits || 0}.`
      });
      return;
    }

    setIsGenerating(true);

    try {
      // Prepare submission data
      const submissionData = {
        ...formData,
        templateId: 'MemeVideoTemplate',
        videoSource: formData.uploadedFiles?.[0] || formData.videoSource
      };

      const result = await triggerMemeVideoGeneration(submissionData);

      if (result.success) {
        console.log("Inngest workflow triggered successfully:", result.eventId);
        toast.success("Generation Started!", {
          description: "Your Meme video is being generated in the background. Check your dashboard for updates.",
        });

        // Optimistically update credits in UI
        setUserDetail(prev => prev ? {
          ...prev,
          credits: Math.max(0, prev.credits - VIDEO_GENERATION_COST)
        } : null);

        // Redirect to dashboard after triggering
        router.push('/dashboard');
      } else {
        console.error("Failed to trigger Inngest workflow:", result.error);
        toast.error("Generation Failed", {
          description: result.error || "An error occurred triggering the generation process.",
        });
      }

    } catch (error) {
      console.error("Error calling triggerMemeVideoGeneration server action:", error);
      toast.error("Generation Failed", {
        description: error.message || "An unexpected error occurred.",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Check if form is valid for generation
  const canGenerate = formData.projectTitle?.trim() &&
                     (formData.uploadedFiles?.length || formData.videoSource) &&
                     formData.memeText?.trim() &&
                     userDetail?.credits >= VIDEO_GENERATION_COST;


  // Show loading state while checking authentication
  if (!isLoaded) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // Only render the page content if the user is signed in
  if (!isSignedIn) {
    return null;
  }

  // Header content
  const headerContent = (
    <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50 via-pink-50 to-purple-50 dark:from-purple-950/20 dark:via-pink-950/20 dark:to-purple-950/20 p-8 border border-border/50">
      <div className="relative z-10">
        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
          <div className="space-y-3">
            <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              Meme Video Creator 🎭
            </h1>
            <p className="text-lg text-muted-foreground max-w-2xl">
              Transform any video into viral meme content with custom text, styling, and effects.
              Perfect for social media engagement and entertainment.
            </p>
          </div>
          <div className="flex items-center gap-4">
            <div className="text-right">
              <p className="text-sm text-muted-foreground">Credits Available</p>
              <p className="text-2xl font-bold text-primary">
                {userDetail?.credits || 0}
              </p>
            </div>
          </div>
        </div>
      </div>
      {/* Background decoration */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-2xl"></div>
    </div>
  );

  return (
    <VideoCreationLayout
      header={headerContent}
      configurationPanel={
        <MemeVideoConfigurationForm
          formData={formData}
          errors={errors}
          onFieldChange={updateField}
          sectionProps={{
            project: {
              title: "Meme Video Project",
              description: "Set up your meme video project",
              showDescription: false
            },
            media: {
              title: "Upload Video",
              description: "Upload the video you want to turn into a meme",
              acceptedTypes: ['video/*'],
              multiple: false,
              maxFiles: 1
            },
            aspectRatio: {
              title: "Meme Format",
              description: "Choose the format for your meme video",
              size: "sm"
            }
          }}
        />
      }
      previewPanel={
        <MemeVideoPreviewPanel
          configuration={formData}
          credits={userDetail?.credits || 0}
          onGenerate={handleGenerateVideo}
          isGenerating={isGenerating}
          status={isGenerating ? 'processing' : 'pending'}
          canGenerate={canGenerate}
          generationError={Object.keys(errors).length > 0 ? 'Please fix form errors before generating' : null}
        />
      }
    />
  );
}

export default CreateMemeVideoPage;
