
"use client";

import React, { useState, useContext, useEffect } from 'react'; // Import useContext and useEffect
import { useUser } from "@clerk/nextjs"; // Import useUser
import { useRouter } from 'next/navigation'; // Import useRouter
import { <PERSON>rk<PERSON>, Loader2 } from "lucide-react";
import { Input } from "@/components/ui/input"; // Import Input for Project Title
import { Label } from "@/components/ui/label"; // Import Label for Project Title
import { Button } from "@/components/ui/button"; // Import Button
import { toast } from "sonner"; // Import toast

// Assuming this context exists and provides { userDetail: { credits: number }, setUserDetail: Function }
import { UserDetailContext } from "@/context/UserDetailContext"; // Import the UserDetailContext

// Import your form section components here as you create them
import VideoInput from "./_components/VideoInput";
import TextInput from "./_components/TextInput";
import LivePreview from "./_components/LivePreview";
import TextStylingOptions from "./_components/TextStylingOptions";
import PositioningOptions from "./_components/PositioningOptions";
import AISuggestions from "./_components/AISuggestions";
import MemeStylePresets from "./_components/MemeStylePresets";
import AspectRatioControl from "./_components/AspectRatioControl";
import AudioOptions from "./_components/AudioOptions";

// Import the new server action
import { triggerMemeVideoGeneration } from "@/actions/memeVideoGeneration"; // Import the server action

// Import the constant for video generation cost
import { VIDEO_GENERATION_COST } from '@/lib/creditUtils'; // Import cost constant

function CreateMemeVideoPage() {
  const { isLoaded, isSignedIn } = useUser(); // Use the useUser hook
  const router = useRouter(); // Use the useRouter hook
  const { userDetail, setUserDetail } = useContext(UserDetailContext); // Use the UserDetailContext and get setUserDetail

  // Redirect unauthenticated users to the sign-in page
  useEffect(() => {
    if (isLoaded && !isSignedIn) {
      router.push('/sign-in');
    }
  }, [isLoaded, isSignedIn, router]);


  // --- State for Form Inputs ---
  const [projectTitle, setProjectTitle] = useState(""); // Added Project Title state
  const [videoSource, setVideoSource] = useState(null); // File object or URL
  const [memeText, setMemeText] = useState("");
  const [font, setFont] = useState("Impact"); // Default meme font
  const [fontSize, setFontSize] = useState(48);
  const [textColor, setTextColor] = useState("#FFFFFF"); // Default white
  const [textOutline, setTextOutline] = useState(true);
  const [outlineColor, setOutlineColor] = useState("#000000"); // Default black
  const [outlineThickness, setOutlineThickness] = useState(2);
  const [textShadow, setTextShadow] = useState(false);
  const [backgroundColor, setBackgroundColor] = useState(null); // Optional background box
  const [textPosition, setTextPosition] = useState("bottom-center"); // Presets like top-center, middle-center, bottom-center
  const [aspectRatio, setAspectRatio] = useState('9:16'); // Default for shorts
  const [videoStartTime, setVideoStartTime] = useState(0); // For trimming
  const [videoEndTime, setVideoEndTime] = useState(null); // For trimming
  const [useOriginalAudio, setUseOriginalAudio] = useState(true);
  const [backgroundMusic, setBackgroundMusic] = useState(null); // Audio file or selection
  const [originalAudioVolume, setOriginalAudioVolume] = useState(1.0);
  const [backgroundMusicVolume, setBackgroundMusicVolume] = useState(0.5);

  // --- State for Generation Process (Local) ---
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationMessage, setGenerationMessage] = useState("");
  // Remove generatedVideoUrl state as it will be handled by PlayerDialog later
  // const [generatedVideoUrl, setGeneratedVideoUrl] = useState(null);


  // --- Handle Main Video Generation (Trigger Inngest) ---
  const handleGenerateMemeVideo = async () => {
    console.log("Initiating Meme video generation via server action...");

    // Combine form data for the server action
    const formData = {
      projectTitle,
      videoSource, // This could be a URL or an uploaded file ID
      memeText,
      font,
      fontSize,
      textColor,
      textOutline,
      outlineColor,
      outlineThickness,
      textShadow,
      backgroundColor,
      textPosition,
      aspectRatio,
      videoStartTime,
      videoEndTime,
      useOriginalAudio,
      backgroundMusic,
      originalAudioVolume,
      backgroundMusicVolume,
      // Add templateId here, assuming a default or selected one
      templateId: 'MemeVideoTemplate', // Use the correct template ID
    };

    // --- Form Validation (Client-side) ---
     if (!formData.projectTitle || !formData.videoSource || !formData.memeText || !formData.font || !formData.fontSize || !formData.textColor || formData.textOutline === null || formData.outlineColor === null || formData.outlineThickness === null || formData.textShadow === null || formData.textPosition === null || formData.aspectRatio === null || formData.videoStartTime === null || formData.useOriginalAudio === null || formData.originalAudioVolume === null || formData.backgroundMusicVolume === null) {
         toast.error("Missing Information", { description: "Please fill in all required fields." });
         return;
     }
     // Add more specific validation for videoSource (URL format or uploaded file ID type)
    // --- End Form Validation ---

    // --- Credit Check (Frontend - for immediate feedback) ---
    // Assuming Meme video generation has a cost
    if (!userDetail || userDetail.credits < VIDEO_GENERATION_COST) { // Use the same cost for now
      toast.error("Insufficient Credits", { description: `Need ${VIDEO_GENERATION_COST}, have ${userDetail?.credits || 0}.` });
      return;
    }

    setIsGenerating(true);
    setGenerationMessage("Triggering generation process...");

    try {
      // Call the new server action to trigger the Inngest workflow
      const result = await triggerMemeVideoGeneration(formData);

      if (result.success) {
        console.log("Inngest workflow triggered successfully:", result.eventId);
        toast.success("Generation Started!", {
          description: "Your Meme video is being generated in the background. Check your dashboard for updates.",
        });
         // Optimistically update credits in UI
        setUserDetail(prev => prev ? { ...prev, credits: Math.max(0, prev.credits - VIDEO_GENERATION_COST) } : null);
        // Redirect to dashboard after triggering
        router.push('/dashboard');
      } else {
        console.error("Failed to trigger Inngest workflow:", result.error);
        toast.error("Generation Failed", {
          description: result.error || "An error occurred triggering the generation process.",
        });
      }

    } catch (error) {
      console.error("Error calling triggerMemeVideoGeneration server action:", error);
      toast.error("Generation Failed", {
        description: error.message || "An unexpected error occurred.",
      });
    } finally {
      setIsGenerating(false);
      setGenerationMessage("");
    }
  };

  const isGenerateButtonDisabled = isGenerating || // Disable during processing
                                   !videoSource || !memeText || !font || !fontSize || !textColor || textOutline === null || outlineColor === null || outlineThickness === null || textShadow === null || textPosition === null || aspectRatio === null || videoStartTime === null || useOriginalAudio === null || originalAudioVolume === null || backgroundMusicVolume === null || // Form validation
                                   !userDetail || userDetail.credits < VIDEO_GENERATION_COST; // Credit check


  return (
    
     <>
 <div className="grid grid-cols-1 gap-6 p-4 md:grid-cols-3 md:gap-8 md:p-8">
        <div className="col-span-1 space-y-6 md:col-span-2">
          {/* --- Input Area --- */}
          <div className="p-6 rounded-lg shadow-md space-y-6">
          

            {/* Project Title Input */}
            <div className="space-y-2">
              <label htmlFor="projectTitle" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Project Title (Optional)</label>
              <input type="text" id="projectTitle" value={projectTitle} onChange={(e) => setProjectTitle(e.target.value)} placeholder="e.g., My Awesome AI Video" className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm p-2"/>
            </div>

            {/* Video Source Input Component */}
            <VideoInput videoSource={videoSource} onVideoSourceChange={setVideoSource} />

            {/* Primary Text Input Component */}
            <TextInput memeText={memeText} onMemeTextChange={setMemeText} />
          </div>

          {/* --- Customization & AI Tools --- */}
          <div className="p-6 rounded-lg shadow-md space-y-6">
            <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-200">Make it Pop</h2>

            {/* Text Styling Options Component */}
            <TextStylingOptions
                font={font} onFontChange={setFont}
                fontSize={fontSize} onFontSizeChange={setFontSize}
                textColor={textColor} onTextColorChange={setTextColor}
                textOutline={textOutline} onTextOutlineChange={setTextOutline}
                outlineColor={outlineColor} onOutlineColorChange={setOutlineColor}
                outlineThickness={outlineThickness} onOutlineThicknessChange={setOutlineThickness} // Corrected prop name
                textShadow={textShadow} onTextShadowChange={setTextShadow}
                backgroundColor={backgroundColor} onBackgroundColorChange={setBackgroundColor}
            />

            {/* Positioning Options Component */}
            <PositioningOptions textPosition={textPosition} onTextPositionChange={setTextPosition} />

            {/* AI-Powered Suggestions Component */}
            <AISuggestions onTextSuggest={setMemeText} />

             {/* Meme Style Presets Component */}
            <MemeStylePresets onPresetSelect={(preset) => {
                setFont(preset.font);
                setFontSize(preset.fontSize);
                setTextColor(preset.textColor);
                setTextOutline(preset.textOutline);
                setOutlineColor(preset.outlineColor);
                setOutlineThickness(preset.outlineThickness); // Corrected state update
                setTextShadow(preset.textShadow);
                setBackgroundColor(preset.backgroundColor);
                setTextPosition(preset.textPosition);
            }} />
          </div>

          {/* --- Video & Audio Controls --- */}
          <div className="p-6 rounded-lg shadow-md space-y-6">
            <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-200">Fine-Tuning</h2>

            {/* Aspect Ratio Control Component */}
            <AspectRatioControl selectedAspectRatio={aspectRatio} onAspectRatioSelect={setAspectRatio} />

            {/* Video Trimming Component */}
           
            {/* Audio Options Component */}
            <AudioOptions
                useOriginalAudio={useOriginalAudio} onUseOriginalAudioChange={setUseOriginalAudio}
                backgroundMusic={backgroundMusic} onBackgroundMusicChange={setBackgroundMusic}
                originalAudioVolume={originalAudioVolume} onOriginalAudioVolumeChange={setOriginalAudioVolume}
                backgroundMusicVolume={backgroundMusicVolume} onBackgroundMusicVolumeChange={setBackgroundMusicVolume}
            />
          </div>
        </div>

       
        <div className="col-span-1 space-y-6">
          <div className="p-6 rounded-lg shadow-md space-y-6">
            <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-200">Preview & Generate</h2>
            {/* Live Preview Pane Component */}
            <LivePreview
              videoSource={videoSource}
              memeText={memeText}
              font={font}
              fontSize={fontSize}
              textColor={textColor}
              textOutline={textOutline}
              outlineColor={outlineColor}
              outlineThickness={outlineThickness}
              textShadow={textShadow}
              backgroundColor={backgroundColor}
              textPosition={textPosition}
              aspectRatio={aspectRatio}
            />


           
            <Button
              onClick={handleGenerateMemeVideo}
              disabled={isGenerateButtonDisabled}
              className="w-full" size="lg"
              title={!userDetail || userDetail.credits < VIDEO_GENERATION_COST ? `Insufficient credits (Need ${VIDEO_GENERATION_COST})` : (isGenerateButtonDisabled ? "Please complete all fields" : (isGenerating ? generationMessage : "Generate Meme Video"))} // Updated title
            >
              {isGenerating ? ( <Loader2 className="mr-2 h-5 w-5 animate-spin" /> ) : ( <Sparkles className="mr-2 h-5 w-5" /> )}
              {isGenerating ? generationMessage : "Generate Meme Video"}
            </Button>

            {/* Display Generated Video */}
            {/* Removed generated video display */}
            {/* {generatedVideoUrl && (
              <div className="mt-4">
                <h3 className="text-xl font-semibold mb-2 text-gray-800 dark:text-gray-200">Generated Video</h3>
                <video src={generatedVideoUrl} controls className="w-full rounded-md shadow-md"></video>
                <a href={generatedVideoUrl} download="meme_video.mp4">
                   <Button className="w-full mt-2">Download Video</Button>
                </a>
              </div>
            )} */}
          </div>
        </div>
        </div>
    </>
  );
}

export default CreateMemeVideoPage;
