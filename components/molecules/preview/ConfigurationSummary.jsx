/**
 * ConfigurationSummary Molecule
 * Displays a summary of current video configuration settings
 * Used in preview panels across all video creation workflows
 */

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { SectionHeader } from '@/components/atoms/layout';
import { 
  Settings, 
  FileText, 
  Image, 
  Volume2, 
  Clock, 
  AspectRatio,
  User,
  Palette,
  Music
} from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * @param {Object} props
 * @param {Object} props.configuration - Configuration object with video settings
 * @param {string} props.className - Additional CSS classes
 * @param {string} props.title - Summary title
 * @param {boolean} props.showHeader - Whether to show section header
 * @param {string} props.size - Component size variant
 * @param {Array} props.excludeFields - Fields to exclude from summary
 */
export default function ConfigurationSummary({
  configuration = {},
  className,
  title = "Configuration Summary",
  showHeader = true,
  size = 'md',
  excludeFields = [],
  ...props
}) {
  const getFieldIcon = (field) => {
    const iconMap = {
      projectTitle: FileText,
      script: FileText,
      aspectRatio: AspectRatio,
      duration: Clock,
      voice: User,
      template: Palette,
      backgroundMusic: Music,
      volume: Volume2,
      images: Image,
      style: Palette,
      creator: User
    };
    return iconMap[field] || Settings;
  };

  const formatValue = (key, value) => {
    if (!value) return 'Not set';
    
    switch (key) {
      case 'script':
      case 'aiPrompt':
        return value.length > 50 ? `${value.substring(0, 50)}...` : value;
      case 'duration':
        return `${value} seconds`;
      case 'volume':
        return `${value}%`;
      case 'aspectRatio':
        return value;
      case 'uploadedFiles':
        return Array.isArray(value) ? `${value.length} file(s)` : '0 files';
      case 'backgroundMusic':
        return value === 'none' ? 'None' : value;
      default:
        if (typeof value === 'boolean') {
          return value ? 'Yes' : 'No';
        }
        if (Array.isArray(value)) {
          return `${value.length} item(s)`;
        }
        return String(value);
    }
  };

  const getFieldLabel = (key) => {
    const labelMap = {
      projectTitle: 'Project Title',
      projectDescription: 'Description',
      script: 'Script',
      aiPrompt: 'AI Prompt',
      manualScript: 'Manual Script',
      aspectRatio: 'Aspect Ratio',
      duration: 'Duration',
      voice: 'Voice',
      template: 'Template',
      backgroundMusic: 'Background Music',
      volume: 'Volume',
      originalAudioVolume: 'Original Audio',
      backgroundMusicVolume: 'Music Volume',
      uploadedFiles: 'Uploaded Files',
      style: 'Style',
      creator: 'AI Creator',
      scriptMode: 'Script Mode',
      videoSource: 'Video Source',
      memeText: 'Meme Text',
      font: 'Font',
      fontSize: 'Font Size',
      textColor: 'Text Color',
      numberOfClips: 'Number of Clips',
      clipDuration: 'Clip Duration',
      userPrompt: 'User Prompt'
    };
    return labelMap[key] || key.charAt(0).toUpperCase() + key.slice(1);
  };

  const isImportantField = (key) => {
    const importantFields = [
      'projectTitle',
      'script',
      'aiPrompt',
      'aspectRatio',
      'voice',
      'template',
      'creator',
      'uploadedFiles',
      'numberOfClips'
    ];
    return importantFields.includes(key);
  };

  // Filter and organize configuration data
  const configEntries = Object.entries(configuration)
    .filter(([key, value]) => {
      // Exclude specified fields
      if (excludeFields.includes(key)) return false;
      // Exclude empty values
      if (value === '' || value === null || value === undefined) return false;
      // Exclude arrays with no items
      if (Array.isArray(value) && value.length === 0) return false;
      return true;
    })
    .sort(([keyA], [keyB]) => {
      // Sort important fields first
      const aImportant = isImportantField(keyA);
      const bImportant = isImportantField(keyB);
      if (aImportant && !bImportant) return -1;
      if (!aImportant && bImportant) return 1;
      return 0;
    });

  if (configEntries.length === 0) {
    return (
      <Card className={cn("", className)} {...props}>
        {showHeader && (
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Settings className="h-4 w-4" />
              {title}
            </CardTitle>
          </CardHeader>
        )}
        <CardContent className="text-center text-muted-foreground py-8">
          <Settings className="h-8 w-8 mx-auto mb-2 opacity-50" />
          <p className="text-sm">No configuration set</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("", className)} {...props}>
      {showHeader && (
        <CardHeader className={cn(
          "pb-3",
          {
            'p-3': size === 'sm',
            'p-4': size === 'md',
            'p-6': size === 'lg',
          }
        )}>
          <CardTitle className={cn(
            "flex items-center gap-2",
            {
              'text-sm': size === 'sm',
              'text-base': size === 'md',
              'text-lg': size === 'lg',
            }
          )}>
            <Settings className="h-4 w-4" />
            {title}
          </CardTitle>
        </CardHeader>
      )}
      
      <CardContent className={cn(
        "space-y-3",
        {
          'p-3 pt-0': size === 'sm',
          'p-4 pt-0': size === 'md',
          'p-6 pt-0': size === 'lg',
        }
      )}>
        {configEntries.map(([key, value], index) => {
          const IconComponent = getFieldIcon(key);
          const isImportant = isImportantField(key);
          
          return (
            <div key={key}>
              <div className="flex items-start gap-3">
                <IconComponent className={cn(
                  "flex-shrink-0 mt-0.5",
                  {
                    'h-3 w-3': size === 'sm',
                    'h-4 w-4': size === 'md',
                    'h-5 w-5': size === 'lg',
                  },
                  isImportant ? 'text-primary' : 'text-muted-foreground'
                )} />
                
                <div className="flex-1 min-w-0 space-y-1">
                  <div className="flex items-center gap-2">
                    <span className={cn(
                      "font-medium",
                      {
                        'text-xs': size === 'sm',
                        'text-sm': size === 'md',
                        'text-base': size === 'lg',
                      }
                    )}>
                      {getFieldLabel(key)}
                    </span>
                    {isImportant && (
                      <Badge variant="secondary" className="text-xs">
                        Required
                      </Badge>
                    )}
                  </div>
                  
                  <p className={cn(
                    "text-muted-foreground break-words",
                    {
                      'text-xs': size === 'sm',
                      'text-sm': size === 'md',
                      'text-base': size === 'lg',
                    }
                  )}>
                    {formatValue(key, value)}
                  </p>
                </div>
              </div>
              
              {index < configEntries.length - 1 && (
                <Separator className="mt-3" />
              )}
            </div>
          );
        })}
      </CardContent>
    </Card>
  );
}

/**
 * Specialized configuration summary variants for different video types
 */

export function AIVideoConfigSummary(props) {
  return (
    <ConfigurationSummary
      title="AI Video Configuration"
      excludeFields={['errors', 'isGenerating', 'touched']}
      {...props}
    />
  );
}

export function MemeVideoConfigSummary(props) {
  return (
    <ConfigurationSummary
      title="Meme Video Configuration"
      excludeFields={['errors', 'isGenerating', 'touched']}
      {...props}
    />
  );
}

export function PodcastClipperConfigSummary(props) {
  return (
    <ConfigurationSummary
      title="Podcast Clipper Configuration"
      excludeFields={['errors', 'isClipping', 'results']}
      {...props}
    />
  );
}

export function CompactConfigSummary(props) {
  return (
    <ConfigurationSummary
      size="sm"
      showHeader={false}
      {...props}
    />
  );
}
