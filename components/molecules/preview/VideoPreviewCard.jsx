/**
 * VideoPreviewCard Molecule
 * Reusable video preview card used across all video creation workflows
 * Shows video preview, status, metadata, and generation controls
 */

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { StatusBadge } from '@/components/atoms/indicators';
import { LoadingSpinner } from '@/components/atoms/display';
import { Container } from '@/components/atoms/layout';
import { Play, Pause, Download, Eye } from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * @param {Object} props
 * @param {string} props.title - Card title
 * @param {string} props.videoUrl - Video URL for preview
 * @param {string} props.thumbnailUrl - Thumbnail image URL
 * @param {string} props.status - Video generation status
 * @param {string} props.duration - Video duration
 * @param {string} props.aspectRatio - Video aspect ratio
 * @param {number} props.credits - User credits
 * @param {number} props.requiredCredits - Credits required for generation
 * @param {string} props.className - Additional CSS classes
 * @param {boolean} props.showCredits - Whether to show credit information
 * @param {boolean} props.showStatus - Whether to show status badge
 * @param {boolean} props.isLoading - Whether preview is loading
 * @param {React.ReactNode} props.children - Additional content
 * @param {React.ReactNode} props.actions - Action buttons/controls
 * @param {string} props.size - Card size variant
 */
export default function VideoPreviewCard({
  title = "Video Preview",
  videoUrl,
  thumbnailUrl,
  status = 'pending',
  duration,
  aspectRatio = '16:9',
  credits,
  requiredCredits,
  className,
  showCredits = true,
  showStatus = true,
  isLoading = false,
  children,
  actions,
  size = 'md',
  ...props
}) {
  const aspectRatioClasses = {
    '16:9': 'aspect-video',
    '9:16': 'aspect-[9/16]',
    '1:1': 'aspect-square',
    '4:3': 'aspect-[4/3]',
    '21:9': 'aspect-[21/9]'
  };

  const renderPreviewContent = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-full bg-muted">
          <LoadingSpinner size="lg" showMessage message="Loading preview..." />
        </div>
      );
    }

    if (videoUrl) {
      return (
        <video
          src={videoUrl}
          poster={thumbnailUrl}
          controls
          className="w-full h-full object-cover"
          preload="metadata"
        >
          Your browser does not support the video tag.
        </video>
      );
    }

    if (thumbnailUrl) {
      return (
        <div className="relative w-full h-full">
          <img
            src={thumbnailUrl}
            alt="Video thumbnail"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 flex items-center justify-center bg-black/20">
            <Play className="h-12 w-12 text-white opacity-80" />
          </div>
        </div>
      );
    }

    return (
      <div className="flex flex-col items-center justify-center h-full bg-muted text-muted-foreground">
        <Eye className="h-12 w-12 mb-2 opacity-50" />
        <p className="text-sm">Preview will appear here</p>
      </div>
    );
  };

  return (
    <Card className={cn("overflow-hidden", className)} {...props}>
      <CardHeader className={cn(
        "pb-3",
        {
          'p-3': size === 'sm',
          'p-4': size === 'md',
          'p-6': size === 'lg',
        }
      )}>
        <div className="flex items-center justify-between">
          <CardTitle className={cn(
            {
              'text-base': size === 'sm',
              'text-lg': size === 'md',
              'text-xl': size === 'lg',
            }
          )}>
            {title}
          </CardTitle>
          {showStatus && <StatusBadge status={status} size="sm" />}
        </div>
      </CardHeader>
      
      <CardContent className={cn(
        "space-y-4",
        {
          'p-3 pt-0': size === 'sm',
          'p-4 pt-0': size === 'md',
          'p-6 pt-0': size === 'lg',
        }
      )}>
        {/* Video Preview */}
        <div className={cn(
          "relative rounded-lg overflow-hidden bg-muted border",
          aspectRatioClasses[aspectRatio] || aspectRatioClasses['16:9']
        )}>
          {renderPreviewContent()}
        </div>
        
        {/* Metadata */}
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <div className="flex items-center gap-4">
            {duration && <span>Duration: {duration}</span>}
            {aspectRatio && <span>Ratio: {aspectRatio}</span>}
          </div>
          
          {showCredits && requiredCredits && (
            <div className="flex items-center gap-2">
              <span className={cn(
                "font-medium",
                credits !== undefined && credits < requiredCredits 
                  ? "text-destructive" 
                  : "text-foreground"
              )}>
                {requiredCredits} credits
              </span>
              {credits !== undefined && (
                <span className="text-xs">
                  ({credits} available)
                </span>
              )}
            </div>
          )}
        </div>
        
        {/* Additional content */}
        {children}
        
        {/* Actions */}
        {actions && (
          <div className="pt-4 border-t border-border/30">
            {actions}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * Specialized video preview card variants for different video types
 */

export function AIVideoPreviewCard(props) {
  return (
    <VideoPreviewCard
      title="AI Video Preview"
      requiredCredits={10}
      {...props}
    />
  );
}

export function MemeVideoPreviewCard(props) {
  return (
    <VideoPreviewCard
      title="Meme Video Preview"
      requiredCredits={5}
      {...props}
    />
  );
}

export function PodcastClipperPreviewCard(props) {
  return (
    <VideoPreviewCard
      title="Podcast Clips Preview"
      requiredCredits={8}
      {...props}
    />
  );
}

export function AIUGCPreviewCard(props) {
  return (
    <VideoPreviewCard
      title="AI UGC Video Preview"
      requiredCredits={15}
      {...props}
    />
  );
}

export function CompactVideoPreviewCard(props) {
  return (
    <VideoPreviewCard
      size="sm"
      showCredits={false}
      {...props}
    />
  );
}
