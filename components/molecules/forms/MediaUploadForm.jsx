/**
 * MediaUploadForm Molecule
 * Reusable media upload form with drag & drop, preview, and validation
 * Used in Meme Video, Podcast Clipper, AI UGC Video workflows
 */

import React, { useCallback, useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { SectionHeader } from '@/components/atoms/layout';
import { StatusBadge, UploadStatus } from '@/components/atoms/indicators';
import { LoadingSpinner } from '@/components/atoms/display';
import { ErrorMessage } from '@/components/atoms/display';
import { Upload, File, X, Image, Video, Music, FileText } from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * @param {Object} props
 * @param {Array} props.uploadedFiles - Array of uploaded file objects
 * @param {Array} props.uploadingFiles - Array of files currently uploading
 * @param {Function} props.onFileUpload - File upload handler
 * @param {Function} props.onRemoveFile - File removal handler
 * @param {Array} props.acceptedTypes - Accepted file types
 * @param {number} props.maxFiles - Maximum number of files
 * @param {number} props.maxSizeBytes - Maximum file size in bytes
 * @param {string} props.className - Additional CSS classes
 * @param {string} props.title - Section title
 * @param {string} props.description - Section description
 * @param {boolean} props.multiple - Whether to allow multiple files
 * @param {string} props.uploadText - Upload button text
 * @param {string} props.dragText - Drag and drop text
 */
export default function MediaUploadForm({
  uploadedFiles = [],
  uploadingFiles = [],
  onFileUpload,
  onRemoveFile,
  acceptedTypes = ['image/*', 'video/*', 'audio/*'],
  maxFiles = 5,
  maxSizeBytes = 100 * 1024 * 1024, // 100MB
  className,
  title = "Upload Media",
  description = "Upload your media files",
  multiple = true,
  uploadText = "Choose Files",
  dragText = "Drag and drop files here, or click to select",
  ...props
}) {
  const [dragActive, setDragActive] = useState(false);
  const [error, setError] = useState('');

  const getFileIcon = (file) => {
    const type = file.type || '';
    if (type.startsWith('image/')) return Image;
    if (type.startsWith('video/')) return Video;
    if (type.startsWith('audio/')) return Music;
    return FileText;
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const validateFile = (file) => {
    if (file.size > maxSizeBytes) {
      return `File size must be less than ${formatFileSize(maxSizeBytes)}`;
    }
    
    const isValidType = acceptedTypes.some(type => {
      if (type.endsWith('/*')) {
        return file.type.startsWith(type.slice(0, -1));
      }
      return file.type === type;
    });
    
    if (!isValidType) {
      return `File type not supported. Accepted types: ${acceptedTypes.join(', ')}`;
    }
    
    return null;
  };

  const handleFiles = useCallback((files) => {
    setError('');
    const fileArray = Array.from(files);
    
    if (!multiple && fileArray.length > 1) {
      setError('Only one file is allowed');
      return;
    }
    
    if (uploadedFiles.length + fileArray.length > maxFiles) {
      setError(`Maximum ${maxFiles} files allowed`);
      return;
    }
    
    for (const file of fileArray) {
      const validationError = validateFile(file);
      if (validationError) {
        setError(validationError);
        return;
      }
    }
    
    onFileUpload?.(fileArray);
  }, [uploadedFiles.length, maxFiles, multiple, onFileUpload, acceptedTypes, maxSizeBytes]);

  const handleDrag = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files);
    }
  }, [handleFiles]);

  const handleInputChange = useCallback((e) => {
    e.preventDefault();
    if (e.target.files && e.target.files[0]) {
      handleFiles(e.target.files);
    }
  }, [handleFiles]);

  return (
    <div className={cn("space-y-6", className)} {...props}>
      <SectionHeader
        title={title}
        description={description}
      />
      
      {/* Upload Area */}
      <Card className={cn(
        "border-2 border-dashed transition-colors",
        dragActive ? "border-primary bg-primary/5" : "border-muted-foreground/25",
        "hover:border-primary/50 hover:bg-primary/5"
      )}>
        <CardContent className="p-6">
          <div
            className="text-center"
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <Upload className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <p className="text-lg font-medium mb-2">{dragText}</p>
            <p className="text-sm text-muted-foreground mb-4">
              Supported formats: {acceptedTypes.join(', ')} • Max size: {formatFileSize(maxSizeBytes)}
            </p>
            
            <input
              type="file"
              multiple={multiple}
              accept={acceptedTypes.join(',')}
              onChange={handleInputChange}
              className="hidden"
              id="file-upload"
            />
            
            <Button asChild variant="outline">
              <label htmlFor="file-upload" className="cursor-pointer">
                <Upload className="mr-2 h-4 w-4" />
                {uploadText}
              </label>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <ErrorMessage
          message={error}
          variant="error"
          size="sm"
          onDismiss={() => setError('')}
          dismissible
        />
      )}

      {/* Uploading Files */}
      {uploadingFiles.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Uploading...</h4>
          {uploadingFiles.map((file, index) => (
            <div key={index} className="flex items-center gap-3 p-3 bg-muted rounded-lg">
              <LoadingSpinner size="sm" />
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">{file.name}</p>
                <p className="text-xs text-muted-foreground">{formatFileSize(file.size)}</p>
              </div>
              <UploadStatus status="uploading" size="sm" />
            </div>
          ))}
        </div>
      )}

      {/* Uploaded Files */}
      {uploadedFiles.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Uploaded Files</h4>
          {uploadedFiles.map((file, index) => {
            const IconComponent = getFileIcon(file);
            return (
              <div key={index} className="flex items-center gap-3 p-3 bg-muted rounded-lg">
                <IconComponent className="h-5 w-5 text-muted-foreground flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">{file.name || file.fileName}</p>
                  <p className="text-xs text-muted-foreground">
                    {file.size ? formatFileSize(file.size) : 'Unknown size'}
                  </p>
                </div>
                <UploadStatus status="uploaded" size="sm" />
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onRemoveFile?.(index)}
                  className="h-8 w-8 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}

/**
 * Specialized media upload form variants for different use cases
 */

export function VideoUploadForm(props) {
  return (
    <MediaUploadForm
      title="Upload Video"
      description="Upload your video file for processing"
      acceptedTypes={['video/*']}
      multiple={false}
      maxFiles={1}
      uploadText="Choose Video"
      dragText="Drag and drop your video here, or click to select"
      {...props}
    />
  );
}

export function AudioUploadForm(props) {
  return (
    <MediaUploadForm
      title="Upload Audio"
      description="Upload your audio file for processing"
      acceptedTypes={['audio/*']}
      multiple={false}
      maxFiles={1}
      uploadText="Choose Audio"
      dragText="Drag and drop your audio file here, or click to select"
      {...props}
    />
  );
}

export function ImageUploadForm(props) {
  return (
    <MediaUploadForm
      title="Upload Images"
      description="Upload background images for your video"
      acceptedTypes={['image/*']}
      multiple={true}
      maxFiles={10}
      uploadText="Choose Images"
      dragText="Drag and drop images here, or click to select"
      {...props}
    />
  );
}
