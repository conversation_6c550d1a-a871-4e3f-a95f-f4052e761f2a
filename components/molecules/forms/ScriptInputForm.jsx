/**
 * ScriptInputForm Molecule
 * Reusable script input form with AI generation and manual input modes
 * Used in AI Video and AI UGC Video workflows
 */

import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { InputFormField, TextareaFormField } from './FormField';
import { SectionHeader } from '@/components/atoms/layout';
import { LoadingSpinner } from '@/components/atoms/display';
import { ErrorMessage } from '@/components/atoms/display';
import { Bot, Edit, Sparkles, RefreshCw } from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * @param {Object} props
 * @param {string} props.scriptMode - Current script mode ('ai' or 'manual')
 * @param {string} props.aiPrompt - AI generation prompt
 * @param {string} props.manualScript - Manual script content
 * @param {string} props.generatedScript - AI generated script
 * @param {Function} props.onScriptModeChange - Script mode change handler
 * @param {Function} props.onAIPromptChange - AI prompt change handler
 * @param {Function} props.onManualScriptChange - Manual script change handler
 * @param {Function} props.onGenerateScript - AI script generation handler
 * @param {boolean} props.isGeneratingScript - Whether AI is generating script
 * @param {Object} props.errors - Validation errors
 * @param {string} props.className - Additional CSS classes
 * @param {string} props.title - Section title
 * @param {string} props.description - Section description
 * @param {boolean} props.showPreview - Whether to show script preview
 */
export default function ScriptInputForm({
  scriptMode = 'ai',
  aiPrompt = '',
  manualScript = '',
  generatedScript = '',
  onScriptModeChange,
  onAIPromptChange,
  onManualScriptChange,
  onGenerateScript,
  isGeneratingScript = false,
  errors = {},
  className,
  title = "Script Content",
  description = "Create your video script using AI or write it manually",
  showPreview = true,
  ...props
}) {
  const [previewExpanded, setPreviewExpanded] = useState(false);

  const handleTabChange = (value) => {
    onScriptModeChange?.(value);
  };

  const currentScript = scriptMode === 'ai' ? generatedScript : manualScript;
  const hasScript = currentScript && currentScript.trim().length > 0;

  return (
    <div className={cn("space-y-6", className)} {...props}>
      <SectionHeader
        title={title}
        description={description}
        icon={<Bot />}
      />
      
      <Tabs value={scriptMode} onValueChange={handleTabChange} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="ai" className="flex items-center gap-2">
            <Bot className="h-4 w-4" />
            AI Generated
          </TabsTrigger>
          <TabsTrigger value="manual" className="flex items-center gap-2">
            <Edit className="h-4 w-4" />
            Manual Script
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="ai" className="space-y-4 mt-6">
          <Card>
            <CardContent className="p-6 space-y-4">
              <InputFormField
                label="Video Topic or Prompt"
                required
                value={aiPrompt}
                onChange={(e) => onAIPromptChange?.(e.target.value)}
                placeholder="Describe what you want your video to be about..."
                error={errors.aiPrompt || errors.prompt}
                description="Be specific about the topic, tone, and key points you want to cover"
              />
              
              <Button
                onClick={onGenerateScript}
                disabled={!aiPrompt.trim() || isGeneratingScript}
                className="w-full"
                variant="default"
              >
                {isGeneratingScript ? (
                  <>
                    <LoadingSpinner size="sm" className="mr-2" />
                    Generating Script...
                  </>
                ) : (
                  <>
                    <Sparkles className="mr-2 h-4 w-4" />
                    Generate Script
                  </>
                )}
              </Button>
              
              {errors.scriptGeneration && (
                <ErrorMessage
                  message={errors.scriptGeneration}
                  variant="error"
                  size="sm"
                />
              )}
            </CardContent>
          </Card>
          
          {/* Generated Script Preview */}
          {generatedScript && (
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-sm font-medium">Generated Script</h4>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onGenerateScript}
                    disabled={isGeneratingScript}
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Regenerate
                  </Button>
                </div>
                
                <div className={cn(
                  "prose prose-sm max-w-none",
                  !previewExpanded && "line-clamp-6"
                )}>
                  <pre className="whitespace-pre-wrap text-sm bg-muted p-4 rounded-lg">
                    {generatedScript}
                  </pre>
                </div>
                
                {generatedScript.length > 300 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setPreviewExpanded(!previewExpanded)}
                    className="mt-2"
                  >
                    {previewExpanded ? 'Show Less' : 'Show More'}
                  </Button>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>
        
        <TabsContent value="manual" className="space-y-4 mt-6">
          <Card>
            <CardContent className="p-6">
              <TextareaFormField
                label="Video Script"
                required
                value={manualScript}
                onChange={(e) => onManualScriptChange?.(e.target.value)}
                placeholder="Write your video script here..."
                error={errors.manualScript || errors.script}
                description="Write the complete script for your video including narration and key points"
                rows={8}
                className="min-h-[200px]"
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      
      {/* Script Summary */}
      {showPreview && hasScript && (
        <Card className="bg-muted/50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between text-sm">
              <span className="font-medium">Script Ready</span>
              <span className="text-muted-foreground">
                {currentScript.length} characters • ~{Math.ceil(currentScript.split(' ').length / 150)} min read
              </span>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

/**
 * Specialized script input form variants for different video types
 */

export function AIVideoScriptForm(props) {
  return (
    <ScriptInputForm
      title="AI Video Script"
      description="Create your AI video script using our AI assistant or write it manually"
      {...props}
    />
  );
}

export function AIUGCScriptForm(props) {
  return (
    <ScriptInputForm
      title="UGC Video Script"
      description="Create your UGC video script - this will be spoken by your selected AI creator"
      {...props}
    />
  );
}

export function CompactScriptForm(props) {
  return (
    <ScriptInputForm
      showPreview={false}
      title="Script"
      description="Create your video script"
      {...props}
    />
  );
}
