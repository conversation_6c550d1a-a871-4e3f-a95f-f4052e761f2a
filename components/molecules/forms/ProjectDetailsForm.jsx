/**
 * ProjectDetailsForm Molecule
 * Reusable project details form used across all video creation workflows
 * Eliminates duplication across AI Video, Meme Video, Podcast Clipper, AI UGC Video
 */

import React from 'react';
import { InputFormField, TextareaFormField } from './FormField';
import { SectionHeader } from '@/components/atoms/layout';
import { cn } from '@/lib/utils';

/**
 * @param {Object} props
 * @param {string} props.projectTitle - Current project title value
 * @param {string} props.projectDescription - Current project description value
 * @param {Function} props.onProjectTitleChange - Project title change handler
 * @param {Function} props.onProjectDescriptionChange - Project description change handler
 * @param {Object} props.errors - Validation errors object
 * @param {string} props.className - Additional CSS classes
 * @param {boolean} props.showDescription - Whether to show description field
 * @param {string} props.titlePlaceholder - Placeholder for title input
 * @param {string} props.descriptionPlaceholder - Placeholder for description input
 * @param {string} props.title - Section title override
 * @param {string} props.description - Section description override
 * @param {boolean} props.required - Whether title is required
 * @param {string} props.size - Form size variant
 */
export default function ProjectDetailsForm({
  projectTitle = '',
  projectDescription = '',
  onProjectTitleChange,
  onProjectDescriptionChange,
  errors = {},
  className,
  showDescription = true,
  titlePlaceholder = "Enter your project title...",
  descriptionPlaceholder = "Describe your project (optional)...",
  title = "Project Details",
  description = "Set up your video project information",
  required = true,
  size = 'md',
  ...props
}) {
  return (
    <div className={cn("space-y-6", className)} {...props}>
      <SectionHeader
        title={title}
        description={description}
        size={size}
        required={required}
      />
      
      <div className="space-y-4">
        <InputFormField
          label="Project Title"
          required={required}
          value={projectTitle}
          onChange={(e) => onProjectTitleChange?.(e.target.value)}
          placeholder={titlePlaceholder}
          error={errors.projectTitle || errors.title}
          className="w-full"
          size={size === 'sm' ? 'sm' : 'md'}
        />
        
        {showDescription && (
          <TextareaFormField
            label="Project Description"
            value={projectDescription}
            onChange={(e) => onProjectDescriptionChange?.(e.target.value)}
            placeholder={descriptionPlaceholder}
            error={errors.projectDescription || errors.description}
            className="w-full"
            rows={3}
          />
        )}
      </div>
    </div>
  );
}

/**
 * Specialized project details form variants for different video types
 */

export function AIVideoProjectDetails(props) {
  return (
    <ProjectDetailsForm
      title="AI Video Project"
      description="Set up your AI-generated video project"
      titlePlaceholder="Name your AI video project..."
      descriptionPlaceholder="Describe the video content you want to create..."
      {...props}
    />
  );
}

export function MemeVideoProjectDetails(props) {
  return (
    <ProjectDetailsForm
      title="Meme Video Project"
      description="Set up your meme video project"
      titlePlaceholder="Name your meme video..."
      descriptionPlaceholder="Describe your meme concept..."
      showDescription={false}
      {...props}
    />
  );
}

export function PodcastClipperProjectDetails(props) {
  return (
    <ProjectDetailsForm
      title="Podcast Clipper Project"
      description="Set up your podcast clipping project"
      titlePlaceholder="Name your podcast clips project..."
      descriptionPlaceholder="Describe the podcast content and clipping goals..."
      {...props}
    />
  );
}

export function AIUGCProjectDetails(props) {
  return (
    <ProjectDetailsForm
      title="AI UGC Video Project"
      description="Set up your AI UGC video project"
      titlePlaceholder="Name your UGC video project..."
      descriptionPlaceholder="Describe the UGC content you want to create..."
      {...props}
    />
  );
}

export function CompactProjectDetails(props) {
  return (
    <ProjectDetailsForm
      size="sm"
      showDescription={false}
      title="Project Info"
      description="Quick project setup"
      {...props}
    />
  );
}
