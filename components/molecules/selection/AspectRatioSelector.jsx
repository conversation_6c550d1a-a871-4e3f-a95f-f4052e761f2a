/**
 * AspectRatioSelector Molecule
 * Visual aspect ratio selection component with preview cards
 * Used in AI Video and Meme Video workflows
 */

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { SectionHeader } from '@/components/atoms/layout';
import { Monitor, Smartphone, Square, Tv, Widescreen } from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * @param {Object} props
 * @param {string} props.value - Selected aspect ratio value
 * @param {Function} props.onChange - Change handler
 * @param {Array} props.options - Custom aspect ratio options
 * @param {string} props.className - Additional CSS classes
 * @param {string} props.title - Section title
 * @param {string} props.description - Section description
 * @param {boolean} props.showLabels - Whether to show ratio labels
 * @param {string} props.size - Component size variant
 */
export default function AspectRatioSelector({
  value = '16:9',
  onChange,
  options,
  className,
  title = "Aspect Ratio",
  description = "Choose the aspect ratio for your video",
  showLabels = true,
  size = 'md',
  ...props
}) {
  const defaultOptions = [
    {
      value: '16:9',
      label: 'Landscape',
      description: 'YouTube, TV',
      icon: Monitor,
      preview: 'aspect-video',
      popular: true
    },
    {
      value: '9:16',
      label: 'Portrait',
      description: 'TikTok, Instagram Stories',
      icon: Smartphone,
      preview: 'aspect-[9/16]',
      popular: true
    },
    {
      value: '1:1',
      label: 'Square',
      description: 'Instagram Posts',
      icon: Square,
      preview: 'aspect-square',
      popular: true
    },
    {
      value: '4:3',
      label: 'Standard',
      description: 'Classic TV',
      icon: Tv,
      preview: 'aspect-[4/3]',
      popular: false
    },
    {
      value: '21:9',
      label: 'Ultrawide',
      description: 'Cinematic',
      icon: Widescreen,
      preview: 'aspect-[21/9]',
      popular: false
    }
  ];

  const ratioOptions = options || defaultOptions;

  const handleSelect = (ratioValue) => {
    onChange?.(ratioValue);
  };

  return (
    <div className={cn("space-y-6", className)} {...props}>
      <SectionHeader
        title={title}
        description={description}
        size={size}
      />
      
      <div className={cn(
        "grid gap-4",
        {
          'grid-cols-2 sm:grid-cols-3': size === 'sm',
          'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3': size === 'md',
          'grid-cols-1 sm:grid-cols-2 lg:grid-cols-5': size === 'lg',
        }
      )}>
        {ratioOptions.map((option) => {
          const IconComponent = option.icon;
          const isSelected = value === option.value;
          
          return (
            <Card
              key={option.value}
              className={cn(
                "cursor-pointer transition-all duration-200 hover:shadow-md",
                isSelected 
                  ? "ring-2 ring-primary border-primary bg-primary/5" 
                  : "hover:border-primary/50",
                option.popular && "relative"
              )}
              onClick={() => handleSelect(option.value)}
            >
              {option.popular && (
                <div className="absolute -top-2 -right-2 bg-primary text-primary-foreground text-xs px-2 py-1 rounded-full">
                  Popular
                </div>
              )}
              
              <CardContent className={cn(
                "p-4 text-center space-y-3",
                {
                  'p-3 space-y-2': size === 'sm',
                  'p-4 space-y-3': size === 'md',
                  'p-6 space-y-4': size === 'lg',
                }
              )}>
                {/* Visual Preview */}
                <div className="flex justify-center">
                  <div className={cn(
                    "border-2 border-muted-foreground/30 rounded",
                    option.preview,
                    {
                      'w-12': size === 'sm',
                      'w-16': size === 'md',
                      'w-20': size === 'lg',
                    },
                    isSelected && "border-primary"
                  )}>
                    <div className="w-full h-full bg-muted rounded-sm flex items-center justify-center">
                      <IconComponent className={cn(
                        "text-muted-foreground",
                        {
                          'h-3 w-3': size === 'sm',
                          'h-4 w-4': size === 'md',
                          'h-5 w-5': size === 'lg',
                        }
                      )} />
                    </div>
                  </div>
                </div>
                
                {/* Labels */}
                {showLabels && (
                  <div className="space-y-1">
                    <div className={cn(
                      "font-medium",
                      {
                        'text-sm': size === 'sm',
                        'text-base': size === 'md',
                        'text-lg': size === 'lg',
                      }
                    )}>
                      {option.value}
                    </div>
                    
                    <div className={cn(
                      "text-muted-foreground",
                      {
                        'text-xs': size === 'sm',
                        'text-sm': size === 'md',
                        'text-base': size === 'lg',
                      }
                    )}>
                      {option.label}
                    </div>
                    
                    {option.description && (
                      <div className={cn(
                        "text-muted-foreground",
                        {
                          'text-xs': size === 'sm' || size === 'md',
                          'text-sm': size === 'lg',
                        }
                      )}>
                        {option.description}
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>
      
      {/* Selected Ratio Info */}
      {value && (
        <div className="text-center text-sm text-muted-foreground">
          Selected: <span className="font-medium text-foreground">{value}</span>
          {ratioOptions.find(opt => opt.value === value)?.description && (
            <span> • {ratioOptions.find(opt => opt.value === value)?.description}</span>
          )}
        </div>
      )}
    </div>
  );
}

/**
 * Specialized aspect ratio selector variants for different use cases
 */

export function VideoAspectRatioSelector(props) {
  return (
    <AspectRatioSelector
      title="Video Aspect Ratio"
      description="Choose the aspect ratio for your video output"
      {...props}
    />
  );
}

export function MemeAspectRatioSelector(props) {
  return (
    <AspectRatioSelector
      title="Meme Format"
      description="Choose the format for your meme video"
      size="sm"
      {...props}
    />
  );
}

export function CompactAspectRatioSelector(props) {
  const compactOptions = [
    { value: '16:9', label: 'Landscape', icon: Monitor, preview: 'aspect-video' },
    { value: '9:16', label: 'Portrait', icon: Smartphone, preview: 'aspect-[9/16]' },
    { value: '1:1', label: 'Square', icon: Square, preview: 'aspect-square' }
  ];
  
  return (
    <AspectRatioSelector
      options={compactOptions}
      size="sm"
      showLabels={false}
      {...props}
    />
  );
}
