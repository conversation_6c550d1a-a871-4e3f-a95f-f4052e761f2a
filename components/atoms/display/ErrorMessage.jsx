/**
 * ErrorMessage Atom
 * Consistent error message component with various display options
 */

import React from 'react';
import { AlertCircle, XCircle, AlertTriangle, Info } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { cn } from '@/lib/utils';

/**
 * @param {Object} props
 * @param {string} props.message - Error message text
 * @param {string} props.title - Optional error title
 * @param {string} props.variant - Error severity variant
 * @param {string} props.size - Error message size
 * @param {boolean} props.showIcon - Whether to show error icon
 * @param {string} props.className - Additional CSS classes
 * @param {React.ReactNode} props.children - Additional content
 * @param {Function} props.onDismiss - Dismiss handler
 * @param {boolean} props.dismissible - Whether error can be dismissed
 */
export default function ErrorMessage({
  message,
  title,
  variant = 'error',
  size = 'md',
  showIcon = true,
  className,
  children,
  onDismiss,
  dismissible = false,
  ...props
}) {
  const variantConfig = {
    error: {
      icon: XCircle,
      className: 'border-red-200 bg-red-50 text-red-800 dark:border-red-800 dark:bg-red-950/20 dark:text-red-400'
    },
    warning: {
      icon: AlertTriangle,
      className: 'border-yellow-200 bg-yellow-50 text-yellow-800 dark:border-yellow-800 dark:bg-yellow-950/20 dark:text-yellow-400'
    },
    info: {
      icon: Info,
      className: 'border-blue-200 bg-blue-50 text-blue-800 dark:border-blue-800 dark:bg-blue-950/20 dark:text-blue-400'
    },
    destructive: {
      icon: AlertCircle,
      className: 'border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive'
    }
  };
  
  const config = variantConfig[variant] || variantConfig.error;
  const IconComponent = config.icon;
  
  // Simple text error message
  if (!title && !children && size === 'sm') {
    return (
      <div 
        className={cn(
          'flex items-center gap-2 text-sm',
          {
            'text-red-600 dark:text-red-400': variant === 'error',
            'text-yellow-600 dark:text-yellow-400': variant === 'warning',
            'text-blue-600 dark:text-blue-400': variant === 'info',
            'text-destructive': variant === 'destructive',
          },
          className
        )}
        {...props}
      >
        {showIcon && (
          <IconComponent className="h-4 w-4 flex-shrink-0" />
        )}
        <span>{message}</span>
      </div>
    );
  }
  
  // Full alert component
  return (
    <Alert 
      className={cn(
        config.className,
        {
          'p-3': size === 'sm',
          'p-4': size === 'md',
          'p-6': size === 'lg',
        },
        className
      )}
      {...props}
    >
      {showIcon && <IconComponent className="h-4 w-4" />}
      
      {title && (
        <AlertTitle className={cn(
          {
            'text-sm': size === 'sm',
            'text-base': size === 'md',
            'text-lg': size === 'lg',
          }
        )}>
          {title}
        </AlertTitle>
      )}
      
      <AlertDescription className={cn(
        {
          'text-xs': size === 'sm',
          'text-sm': size === 'md',
          'text-base': size === 'lg',
        }
      )}>
        {message}
        {children}
      </AlertDescription>
      
      {dismissible && onDismiss && (
        <button
          onClick={onDismiss}
          className="absolute top-2 right-2 p-1 rounded-sm opacity-70 hover:opacity-100 transition-opacity"
          aria-label="Dismiss"
        >
          <XCircle className="h-4 w-4" />
        </button>
      )}
    </Alert>
  );
}

/**
 * Specialized error message variants for common use cases
 */

export function FormFieldError({ error, ...props }) {
  if (!error) return null;
  
  return (
    <ErrorMessage
      message={error}
      variant="error"
      size="sm"
      showIcon={false}
      {...props}
    />
  );
}

export function ValidationError({ errors = [], ...props }) {
  if (!errors.length) return null;
  
  return (
    <ErrorMessage
      title="Please fix the following errors:"
      variant="error"
      size="md"
      {...props}
    >
      <ul className="mt-2 list-disc list-inside space-y-1">
        {errors.map((error, index) => (
          <li key={index} className="text-sm">{error}</li>
        ))}
      </ul>
    </ErrorMessage>
  );
}

export function APIError({ error, retry, ...props }) {
  return (
    <ErrorMessage
      title="Something went wrong"
      message={error?.message || 'An unexpected error occurred. Please try again.'}
      variant="error"
      size="md"
      {...props}
    >
      {retry && (
        <button
          onClick={retry}
          className="mt-3 text-sm underline hover:no-underline"
        >
          Try again
        </button>
      )}
    </ErrorMessage>
  );
}

export function NetworkError({ retry, ...props }) {
  return (
    <ErrorMessage
      title="Connection Error"
      message="Unable to connect to the server. Please check your internet connection and try again."
      variant="warning"
      size="md"
      {...props}
    >
      {retry && (
        <button
          onClick={retry}
          className="mt-3 text-sm underline hover:no-underline"
        >
          Retry
        </button>
      )}
    </ErrorMessage>
  );
}
