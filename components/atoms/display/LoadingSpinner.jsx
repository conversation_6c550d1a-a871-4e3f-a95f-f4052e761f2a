/**
 * LoadingSpinner Atom
 * Reusable loading spinner component with consistent styling and behavior
 */

import React from 'react';
import { Loader2, Rotate<PERSON>w, RefreshCw } from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * @param {Object} props
 * @param {string} props.className - Additional CSS classes
 * @param {string} props.size - Spinner size variant
 * @param {string} props.variant - Spinner style variant
 * @param {string} props.color - Spinner color variant
 * @param {string} props.message - Loading message to display
 * @param {boolean} props.showMessage - Whether to show loading message
 * @param {string} props.iconType - Type of loading icon
 */
export default function LoadingSpinner({
  className,
  size = 'md',
  variant = 'default',
  color = 'primary',
  message = 'Loading...',
  showMessage = false,
  iconType = 'loader',
  ...props
}) {
  const iconComponents = {
    loader: Loader2,
    rotate: RotateCw,
    refresh: RefreshCw
  };
  
  const IconComponent = iconComponents[iconType] || Loader2;
  
  const sizeClasses = {
    'xs': 'h-3 w-3',
    'sm': 'h-4 w-4',
    'md': 'h-6 w-6',
    'lg': 'h-8 w-8',
    'xl': 'h-12 w-12'
  };
  
  const colorClasses = {
    'primary': 'text-primary',
    'secondary': 'text-secondary',
    'muted': 'text-muted-foreground',
    'white': 'text-white',
    'current': 'text-current'
  };
  
  if (showMessage) {
    return (
      <div 
        className={cn(
          'flex items-center justify-center gap-2',
          className
        )}
        {...props}
      >
        <IconComponent 
          className={cn(
            'animate-spin',
            sizeClasses[size],
            colorClasses[color]
          )}
        />
        {message && (
          <span className={cn(
            'text-sm text-muted-foreground',
            {
              'text-xs': size === 'xs' || size === 'sm',
              'text-sm': size === 'md',
              'text-base': size === 'lg',
              'text-lg': size === 'xl',
            }
          )}>
            {message}
          </span>
        )}
      </div>
    );
  }
  
  return (
    <IconComponent 
      className={cn(
        'animate-spin',
        sizeClasses[size],
        colorClasses[color],
        className
      )}
      {...props}
    />
  );
}

/**
 * Specialized loading spinner variants for common use cases
 */

export function ButtonSpinner(props) {
  return (
    <LoadingSpinner
      size="sm"
      color="current"
      {...props}
    />
  );
}

export function PageSpinner({ message = 'Loading page...', ...props }) {
  return (
    <LoadingSpinner
      size="lg"
      color="primary"
      showMessage={true}
      message={message}
      className="py-8"
      {...props}
    />
  );
}

export function InlineSpinner(props) {
  return (
    <LoadingSpinner
      size="xs"
      color="current"
      {...props}
    />
  );
}

export function CardSpinner({ message = 'Loading...', ...props }) {
  return (
    <LoadingSpinner
      size="md"
      color="primary"
      showMessage={true}
      message={message}
      className="py-4"
      {...props}
    />
  );
}

export function VideoGenerationSpinner({ message = 'Generating video...', ...props }) {
  return (
    <LoadingSpinner
      size="lg"
      color="primary"
      showMessage={true}
      message={message}
      iconType="refresh"
      className="py-6"
      {...props}
    />
  );
}
