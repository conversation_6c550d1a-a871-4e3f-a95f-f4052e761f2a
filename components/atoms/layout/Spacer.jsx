/**
 * Spacer Atom
 * Consistent spacing component for layout control
 */

import React from 'react';
import { cn } from '@/lib/utils';

/**
 * @param {Object} props
 * @param {string} props.size - Spacing size variant
 * @param {string} props.direction - Spacing direction (vertical, horizontal, both)
 * @param {string} props.className - Additional CSS classes
 * @param {string} props.as - HTML element to render as
 */
export default function Spacer({
  size = 'md',
  direction = 'vertical',
  className,
  as: Component = 'div',
  ...props
}) {
  const sizeClasses = {
    'xs': {
      vertical: 'h-2',
      horizontal: 'w-2',
      both: 'h-2 w-2'
    },
    'sm': {
      vertical: 'h-4',
      horizontal: 'w-4',
      both: 'h-4 w-4'
    },
    'md': {
      vertical: 'h-6',
      horizontal: 'w-6',
      both: 'h-6 w-6'
    },
    'lg': {
      vertical: 'h-8',
      horizontal: 'w-8',
      both: 'h-8 w-8'
    },
    'xl': {
      vertical: 'h-12',
      horizontal: 'w-12',
      both: 'h-12 w-12'
    },
    '2xl': {
      vertical: 'h-16',
      horizontal: 'w-16',
      both: 'h-16 w-16'
    },
    '3xl': {
      vertical: 'h-24',
      horizontal: 'w-24',
      both: 'h-24 w-24'
    }
  };
  
  return (
    <Component
      className={cn(
        'flex-shrink-0',
        sizeClasses[size]?.[direction] || sizeClasses.md.vertical,
        className
      )}
      aria-hidden="true"
      {...props}
    />
  );
}

/**
 * Specialized spacer variants for common use cases
 */

export function VerticalSpacer({ size = 'md', ...props }) {
  return (
    <Spacer
      size={size}
      direction="vertical"
      {...props}
    />
  );
}

export function HorizontalSpacer({ size = 'md', ...props }) {
  return (
    <Spacer
      size={size}
      direction="horizontal"
      {...props}
    />
  );
}

export function SectionSpacer(props) {
  return (
    <Spacer
      size="3xl"
      direction="vertical"
      {...props}
    />
  );
}

export function FormSpacer(props) {
  return (
    <Spacer
      size="lg"
      direction="vertical"
      {...props}
    />
  );
}
