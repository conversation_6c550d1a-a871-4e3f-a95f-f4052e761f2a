/**
 * SectionHeader Atom
 * Consistent section header component with title, description, and optional actions
 */

import React from 'react';
import { cn } from '@/lib/utils';

/**
 * @param {Object} props
 * @param {string} props.title - Section title
 * @param {string} props.description - Section description
 * @param {React.ReactNode} props.children - Additional content (actions, badges, etc.)
 * @param {string} props.className - Additional CSS classes
 * @param {string} props.size - Header size variant
 * @param {string} props.variant - Header style variant
 * @param {React.ReactNode} props.icon - Optional icon
 * @param {boolean} props.required - Whether to show required indicator
 */
export default function SectionHeader({
  title,
  description,
  children,
  className,
  size = 'md',
  variant = 'default',
  icon,
  required = false,
  ...props
}) {
  return (
    <div 
      className={cn(
        'space-y-1',
        {
          'mb-4': size === 'sm',
          'mb-6': size === 'md',
          'mb-8': size === 'lg',
        },
        className
      )} 
      {...props}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {icon && (
            <div className={cn(
              'flex items-center justify-center rounded-lg',
              {
                'p-1.5 bg-muted': size === 'sm',
                'p-2 bg-gradient-to-br from-primary/10 to-primary/5': size === 'md',
                'p-2.5 bg-gradient-to-br from-primary/10 to-primary/5': size === 'lg',
              }
            )}>
              {React.cloneElement(icon, {
                className: cn(
                  'text-primary',
                  {
                    'h-4 w-4': size === 'sm',
                    'h-5 w-5': size === 'md',
                    'h-6 w-6': size === 'lg',
                  }
                )
              })}
            </div>
          )}
          
          <h3 className={cn(
            'font-semibold text-foreground',
            {
              'text-lg': size === 'sm',
              'text-xl': size === 'md',
              'text-2xl': size === 'lg',
            },
            required && "after:content-['*'] after:ml-1 after:text-destructive"
          )}>
            {title}
          </h3>
        </div>
        
        {children && (
          <div className="flex items-center gap-2">
            {children}
          </div>
        )}
      </div>
      
      {description && (
        <p className={cn(
          'text-muted-foreground',
          {
            'text-sm': size === 'sm',
            'text-base': size === 'md',
            'text-lg': size === 'lg',
          }
        )}>
          {description}
        </p>
      )}
    </div>
  );
}

/**
 * Specialized section header variants for common use cases
 */

export function FormSectionHeader(props) {
  return (
    <SectionHeader
      size="md"
      variant="form"
      {...props}
    />
  );
}

export function PageSectionHeader(props) {
  return (
    <SectionHeader
      size="lg"
      variant="page"
      {...props}
    />
  );
}

export function CardSectionHeader(props) {
  return (
    <SectionHeader
      size="sm"
      variant="card"
      {...props}
    />
  );
}
