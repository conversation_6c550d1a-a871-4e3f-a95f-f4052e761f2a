/**
 * Container Atom
 * Consistent container component with standardized spacing and responsive behavior
 */

import React from 'react';
import { cn } from '@/lib/utils';

/**
 * @param {Object} props
 * @param {React.ReactNode} props.children - Container content
 * @param {string} props.className - Additional CSS classes
 * @param {string} props.size - Container size variant
 * @param {string} props.variant - Container style variant
 * @param {boolean} props.centered - Whether to center content
 * @param {string} props.as - HTML element to render as
 */
export default function Container({
  children,
  className,
  size = 'default',
  variant = 'default',
  centered = false,
  as: Component = 'div',
  ...props
}) {
  return (
    <Component
      className={cn(
        // Base container styles
        'w-full',
        
        // Size variants
        {
          'max-w-sm': size === 'sm',
          'max-w-4xl': size === 'default',
          'max-w-6xl': size === 'lg',
          'max-w-7xl': size === 'xl',
          'max-w-none': size === 'full',
        },
        
        // Variant styles
        {
          'mx-auto px-4 sm:px-6 lg:px-8': variant === 'default',
          'p-6 rounded-lg border bg-card': variant === 'card',
          'p-8 rounded-xl border bg-card shadow-sm': variant === 'elevated',
          'p-4 rounded-md bg-muted/50': variant === 'muted',
        },
        
        // Centering
        centered && 'flex items-center justify-center',
        
        className
      )}
      {...props}
    >
      {children}
    </Component>
  );
}

/**
 * Specialized container variants for common use cases
 */

export function PageContainer(props) {
  return (
    <Container
      size="xl"
      variant="default"
      {...props}
    />
  );
}

export function CardContainer(props) {
  return (
    <Container
      size="default"
      variant="card"
      {...props}
    />
  );
}

export function SectionContainer(props) {
  return (
    <Container
      size="lg"
      variant="default"
      className="py-12"
      {...props}
    />
  );
}

export function FormContainer(props) {
  return (
    <Container
      size="sm"
      variant="elevated"
      {...props}
    />
  );
}
