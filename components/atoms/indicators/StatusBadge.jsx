/**
 * StatusBadge Atom
 * Status indicator component for video generation and processing states
 */

import React from 'react';
import { Badge } from '@/components/ui/badge';
import { 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Loader2,
  Play,
  Pause,
  Upload,
  Download
} from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * @param {Object} props
 * @param {string} props.status - Status type (pending, processing, completed, failed, etc.)
 * @param {boolean} props.showIcon - Whether to show status icon
 * @param {string} props.className - Additional CSS classes
 * @param {string} props.size - Badge size variant
 * @param {string} props.customLabel - Custom label override
 */
export default function StatusBadge({
  status = 'pending',
  showIcon = true,
  className,
  size = 'md',
  customLabel,
  ...props
}) {
  const statusConfig = {
    pending: {
      icon: Clock,
      label: 'Pending',
      className: 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800'
    },
    processing: {
      icon: Loader2,
      label: 'Processing',
      className: 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800',
      animate: true
    },
    completed: {
      icon: CheckCircle,
      label: 'Completed',
      className: 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800'
    },
    failed: {
      icon: XCircle,
      label: 'Failed',
      className: 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800'
    },
    warning: {
      icon: AlertTriangle,
      label: 'Warning',
      className: 'bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900/20 dark:text-orange-400 dark:border-orange-800'
    },
    playing: {
      icon: Play,
      label: 'Playing',
      className: 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800'
    },
    paused: {
      icon: Pause,
      label: 'Paused',
      className: 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800'
    },
    uploading: {
      icon: Upload,
      label: 'Uploading',
      className: 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800',
      animate: true
    },
    downloading: {
      icon: Download,
      label: 'Downloading',
      className: 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800',
      animate: true
    }
  };
  
  const config = statusConfig[status] || statusConfig.pending;
  const IconComponent = config.icon;
  const label = customLabel || config.label;
  
  return (
    <Badge
      className={cn(
        'flex items-center gap-1.5 font-medium border',
        {
          'text-xs px-2 py-1': size === 'sm',
          'text-sm px-2.5 py-1.5': size === 'md',
          'text-base px-3 py-2': size === 'lg',
        },
        config.className,
        className
      )}
      {...props}
    >
      {showIcon && (
        <IconComponent 
          className={cn(
            {
              'h-3 w-3': size === 'sm',
              'h-4 w-4': size === 'md',
              'h-5 w-5': size === 'lg',
            },
            config.animate && 'animate-spin'
          )} 
        />
      )}
      {label}
    </Badge>
  );
}

/**
 * Specialized status badge variants for common use cases
 */

export function VideoGenerationStatus({ status, ...props }) {
  const statusMap = {
    'queued': 'pending',
    'generating': 'processing',
    'rendering': 'processing',
    'completed': 'completed',
    'failed': 'failed',
    'cancelled': 'warning'
  };
  
  return (
    <StatusBadge 
      status={statusMap[status] || status} 
      {...props} 
    />
  );
}

export function UploadStatus({ status, ...props }) {
  const statusMap = {
    'uploading': 'uploading',
    'uploaded': 'completed',
    'upload-failed': 'failed'
  };
  
  return (
    <StatusBadge 
      status={statusMap[status] || status} 
      {...props} 
    />
  );
}

export function ProcessingStatus({ status, ...props }) {
  const statusMap = {
    'queued': 'pending',
    'processing': 'processing',
    'done': 'completed',
    'error': 'failed'
  };
  
  return (
    <StatusBadge 
      status={statusMap[status] || status} 
      {...props} 
    />
  );
}
