/**
 * GenerationButton Atom
 * Reusable button component for video generation with consistent styling and behavior
 */

import React from 'react';
import { Button } from '@/components/ui/button';
import { Loader2, Sparkles, Bot, Film, Scissors, User } from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * @param {Object} props
 * @param {boolean} props.isGenerating - Whether generation is in progress
 * @param {boolean} props.disabled - Whether button is disabled
 * @param {string} props.generationMessage - Message to show during generation
 * @param {string} props.defaultText - Default button text
 * @param {Function} props.onClick - Click handler
 * @param {string} props.className - Additional CSS classes
 * @param {string} props.size - Button size variant
 * @param {string} props.variant - Button style variant
 * @param {React.ReactNode} props.icon - Custom icon (defaults to Sparkles)
 * @param {string} props.title - Tooltip text
 * @param {string} props.videoType - Video type for specialized styling
 * @param {number} props.credits - Current user credits
 * @param {number} props.requiredCredits - Credits required for generation
 */
export default function GenerationButton({
  isGenerating = false,
  disabled = false,
  generationMessage = "Generating...",
  defaultText = "Generate Video",
  onClick,
  className,
  size = "lg",
  variant = "default",
  icon: CustomIcon,
  title,
  videoType = "default",
  credits,
  requiredCredits,
  ...props
}) {
  const isDisabled = disabled || isGenerating;
  const hasSufficientCredits = credits === undefined || requiredCredits === undefined || credits >= requiredCredits;

  // Video type specific icons and styling
  const videoTypeConfig = {
    'ai-video': { icon: Bot, gradient: 'from-blue-500 to-purple-500' },
    'meme-video': { icon: Film, gradient: 'from-purple-500 to-pink-500' },
    'podcast-clipper': { icon: Scissors, gradient: 'from-green-500 to-blue-500' },
    'ai-ugc-video': { icon: User, gradient: 'from-emerald-500 to-teal-500' },
    'default': { icon: Sparkles, gradient: 'from-indigo-500 to-purple-500' }
  };

  const config = videoTypeConfig[videoType] || videoTypeConfig.default;
  const IconComponent = CustomIcon || config.icon;
  
  return (
    <Button
      onClick={onClick}
      disabled={isDisabled || !hasSufficientCredits}
      className={cn(
        "w-full transition-all duration-300",
        videoType !== 'default' && `bg-gradient-to-r ${config.gradient} hover:shadow-lg hover:scale-[1.02]`,
        "disabled:opacity-50 disabled:cursor-not-allowed",
        !hasSufficientCredits && "opacity-60",
        className
      )}
      size={size}
      variant={variant}
      title={title || (!hasSufficientCredits ? `Insufficient credits (Need ${requiredCredits})` : undefined)}
      {...props}
    >
      {isGenerating ? (
        <Loader2 className="mr-2 h-5 w-5 animate-spin" />
      ) : (
        <IconComponent className="mr-2 h-5 w-5" />
      )}
      {isGenerating ? generationMessage : defaultText}
    </Button>
  );
}

/**
 * Specialized generation button variants
 */

export function AIVideoGenerationButton(props) {
  return (
    <GenerationButton
      videoType="ai-video"
      defaultText="Generate AI Video"
      generationMessage="Creating AI Video..."
      requiredCredits={10}
      {...props}
    />
  );
}

export function MemeVideoGenerationButton(props) {
  return (
    <GenerationButton
      videoType="meme-video"
      defaultText="Generate Meme Video"
      generationMessage="Creating Meme Video..."
      requiredCredits={5}
      {...props}
    />
  );
}

export function PodcastClipperButton(props) {
  return (
    <GenerationButton
      videoType="podcast-clipper"
      defaultText="Clip Podcast"
      generationMessage="Processing Podcast..."
      requiredCredits={8}
      {...props}
    />
  );
}

export function AIUGCGenerationButton(props) {
  return (
    <GenerationButton
      videoType="ai-ugc-video"
      defaultText="Generate UGC Video"
      generationMessage="Creating UGC Video..."
      requiredCredits={15}
      {...props}
    />
  );
}
