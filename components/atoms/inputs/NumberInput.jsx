/**
 * NumberInput Atom
 * Standardized number input component with increment/decrement controls
 */

import React from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Minus, Plus } from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * @param {Object} props
 * @param {number} props.value - Current value
 * @param {Function} props.onChange - Change handler
 * @param {number} props.min - Minimum value
 * @param {number} props.max - Maximum value
 * @param {number} props.step - Step increment
 * @param {boolean} props.showControls - Whether to show +/- buttons
 * @param {string} props.className - Additional CSS classes
 * @param {boolean} props.disabled - Whether input is disabled
 * @param {string} props.size - Input size variant
 * @param {string} props.placeholder - Placeholder text
 */
export default function NumberInput({
  value,
  onChange,
  min = 0,
  max = 100,
  step = 1,
  showControls = true,
  className,
  disabled,
  size = 'md',
  placeholder = '0',
  ...props
}) {
  const handleIncrement = () => {
    const newValue = Math.min(max, (value || 0) + step);
    onChange?.(newValue);
  };
  
  const handleDecrement = () => {
    const newValue = Math.max(min, (value || 0) - step);
    onChange?.(newValue);
  };
  
  const handleInputChange = (e) => {
    const newValue = parseFloat(e.target.value);
    if (!isNaN(newValue)) {
      const clampedValue = Math.min(max, Math.max(min, newValue));
      onChange?.(clampedValue);
    } else if (e.target.value === '') {
      onChange?.(undefined);
    }
  };
  
  if (!showControls) {
    return (
      <Input
        type="number"
        value={value ?? ''}
        onChange={handleInputChange}
        min={min}
        max={max}
        step={step}
        disabled={disabled}
        placeholder={placeholder}
        className={cn(
          {
            'h-8 text-sm': size === 'sm',
            'h-10 text-base': size === 'md',
            'h-12 text-lg': size === 'lg',
          },
          className
        )}
        {...props}
      />
    );
  }
  
  return (
    <div className={cn('flex items-center', className)}>
      <Button
        type="button"
        variant="outline"
        size="sm"
        onClick={handleDecrement}
        disabled={disabled || (value !== undefined && value <= min)}
        className={cn(
          'rounded-r-none border-r-0',
          {
            'h-8 w-8 p-0': size === 'sm',
            'h-10 w-10 p-0': size === 'md',
            'h-12 w-12 p-0': size === 'lg',
          }
        )}
      >
        <Minus className={cn(
          {
            'h-3 w-3': size === 'sm',
            'h-4 w-4': size === 'md',
            'h-5 w-5': size === 'lg',
          }
        )} />
      </Button>
      
      <Input
        type="number"
        value={value ?? ''}
        onChange={handleInputChange}
        min={min}
        max={max}
        step={step}
        disabled={disabled}
        placeholder={placeholder}
        className={cn(
          'rounded-none border-x-0 text-center',
          {
            'h-8 text-sm': size === 'sm',
            'h-10 text-base': size === 'md',
            'h-12 text-lg': size === 'lg',
          }
        )}
        {...props}
      />
      
      <Button
        type="button"
        variant="outline"
        size="sm"
        onClick={handleIncrement}
        disabled={disabled || (value !== undefined && value >= max)}
        className={cn(
          'rounded-l-none border-l-0',
          {
            'h-8 w-8 p-0': size === 'sm',
            'h-10 w-10 p-0': size === 'md',
            'h-12 w-12 p-0': size === 'lg',
          }
        )}
      >
        <Plus className={cn(
          {
            'h-3 w-3': size === 'sm',
            'h-4 w-4': size === 'md',
            'h-5 w-5': size === 'lg',
          }
        )} />
      </Button>
    </div>
  );
}

/**
 * Specialized number input variants for common use cases
 */

export function ClipCountInput(props) {
  return (
    <NumberInput
      min={1}
      max={10}
      step={1}
      placeholder="1"
      {...props}
    />
  );
}

export function DurationInput(props) {
  return (
    <NumberInput
      min={5}
      max={300}
      step={5}
      placeholder="60"
      {...props}
    />
  );
}

export function VolumeInput(props) {
  return (
    <NumberInput
      min={0}
      max={100}
      step={5}
      placeholder="50"
      {...props}
    />
  );
}

export function FontSizeInput(props) {
  return (
    <NumberInput
      min={8}
      max={72}
      step={2}
      placeholder="16"
      showControls={false}
      {...props}
    />
  );
}
