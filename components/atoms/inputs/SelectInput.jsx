/**
 * SelectInput Atom
 * Standardized select dropdown component with consistent styling
 */

import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { cn } from '@/lib/utils';

/**
 * @param {Object} props
 * @param {Array} props.options - Array of option objects {value, label, disabled}
 * @param {string} props.value - Selected value
 * @param {Function} props.onValueChange - Change handler
 * @param {string} props.placeholder - Placeholder text
 * @param {boolean} props.disabled - Whether select is disabled
 * @param {string} props.className - Additional CSS classes
 * @param {string} props.size - Select size variant
 * @param {string} props.error - Error state styling
 */
export default function SelectInput({
  options = [],
  value,
  onValueChange,
  placeholder = "Select an option...",
  disabled,
  className,
  size = 'md',
  error,
  ...props
}) {
  return (
    <Select 
      value={value} 
      onValueChange={onValueChange} 
      disabled={disabled}
      {...props}
    >
      <SelectTrigger 
        className={cn(
          {
            'h-8 text-sm': size === 'sm',
            'h-10 text-base': size === 'md',
            'h-12 text-lg': size === 'lg',
          },
          error && 'border-destructive focus:ring-destructive',
          className
        )}
      >
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {options.map((option) => (
          <SelectItem 
            key={option.value} 
            value={option.value}
            disabled={option.disabled}
          >
            {option.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

/**
 * Specialized select input variants for common use cases
 */

export function AspectRatioSelect(props) {
  const aspectRatioOptions = [
    { value: '16:9', label: '16:9 (Landscape)' },
    { value: '9:16', label: '9:16 (Portrait)' },
    { value: '1:1', label: '1:1 (Square)' },
    { value: '4:3', label: '4:3 (Standard)' },
    { value: '21:9', label: '21:9 (Ultrawide)' }
  ];
  
  return (
    <SelectInput
      options={aspectRatioOptions}
      placeholder="Select aspect ratio..."
      {...props}
    />
  );
}

export function VoiceSelect({ voices = [], ...props }) {
  const voiceOptions = voices.map(voice => ({
    value: voice.id || voice.value,
    label: voice.name || voice.label,
    disabled: voice.disabled
  }));
  
  return (
    <SelectInput
      options={voiceOptions}
      placeholder="Select a voice..."
      {...props}
    />
  );
}

export function TemplateSelect({ templates = [], ...props }) {
  const templateOptions = templates.map(template => ({
    value: template.id || template.value,
    label: template.name || template.label,
    disabled: template.disabled
  }));
  
  return (
    <SelectInput
      options={templateOptions}
      placeholder="Select a template..."
      {...props}
    />
  );
}

export function LanguageSelect(props) {
  const languageOptions = [
    { value: 'en', label: 'English' },
    { value: 'es', label: 'Spanish' },
    { value: 'fr', label: 'French' },
    { value: 'de', label: 'German' },
    { value: 'it', label: 'Italian' },
    { value: 'pt', label: 'Portuguese' },
    { value: 'ru', label: 'Russian' },
    { value: 'ja', label: 'Japanese' },
    { value: 'ko', label: 'Korean' },
    { value: 'zh', label: 'Chinese' }
  ];
  
  return (
    <SelectInput
      options={languageOptions}
      placeholder="Select language..."
      {...props}
    />
  );
}

export function QualitySelect(props) {
  const qualityOptions = [
    { value: 'low', label: 'Low (480p)' },
    { value: 'medium', label: 'Medium (720p)' },
    { value: 'high', label: 'High (1080p)' },
    { value: 'ultra', label: 'Ultra (4K)', disabled: true }
  ];
  
  return (
    <SelectInput
      options={qualityOptions}
      placeholder="Select quality..."
      {...props}
    />
  );
}
