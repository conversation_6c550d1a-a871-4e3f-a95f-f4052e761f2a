/**
 * TextInput Atom
 * Standardized text input component with consistent styling and behavior
 */

import React from 'react';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';

/**
 * @param {Object} props
 * @param {string} props.className - Additional CSS classes
 * @param {string} props.error - Error message to display styling
 * @param {boolean} props.disabled - Whether input is disabled
 * @param {boolean} props.loading - Whether input is in loading state
 * @param {string} props.variant - Input style variant
 * @param {string} props.size - Input size variant
 * @param {string} props.value - Input value
 * @param {Function} props.onChange - Change handler
 * @param {string} props.placeholder - Placeholder text
 * @param {string} props.type - Input type
 */
export default function TextInput({
  className,
  error,
  disabled,
  loading,
  variant = 'default',
  size = 'md',
  ...props
}) {
  return (
    <Input
      className={cn(
        // Base styles
        'transition-all duration-200',
        
        // Size variants
        {
          'h-8 text-sm': size === 'sm',
          'h-10 text-base': size === 'md',
          'h-12 text-lg': size === 'lg',
        },
        
        // State styles
        {
          'border-destructive focus-visible:ring-destructive': error,
          'opacity-50 cursor-not-allowed': disabled,
          'animate-pulse': loading,
        },
        
        className
      )}
      disabled={disabled || loading}
      {...props}
    />
  );
}

/**
 * Specialized text input variants for common use cases
 */

export function ProjectTitleInput(props) {
  return (
    <TextInput
      placeholder="Enter your project title..."
      size="lg"
      {...props}
    />
  );
}

export function ScriptInput(props) {
  return (
    <TextInput
      placeholder="Describe your video topic or paste your script..."
      {...props}
    />
  );
}

export function URLInput(props) {
  return (
    <TextInput
      type="url"
      placeholder="https://..."
      {...props}
    />
  );
}

export function SearchInput(props) {
  return (
    <TextInput
      type="search"
      placeholder="Search..."
      size="sm"
      {...props}
    />
  );
}
