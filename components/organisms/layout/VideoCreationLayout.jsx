/**
 * VideoCreationLayout Organism
 * Standard two-column layout for video creation workflows
 * Provides consistent structure across all video creation pages
 */

import React from 'react';
import { Container, PageContainer } from '@/components/atoms/layout';
import { cn } from '@/lib/utils';

/**
 * @param {Object} props
 * @param {React.ReactNode} props.configurationPanel - Left side configuration content
 * @param {React.ReactNode} props.previewPanel - Right side preview content
 * @param {React.ReactNode} props.header - Optional header content
 * @param {React.ReactNode} props.footer - Optional footer content
 * @param {string} props.className - Additional CSS classes
 * @param {string} props.variant - Layout variant
 * @param {boolean} props.fullWidth - Whether to use full width
 * @param {string} props.spacing - Spacing between columns
 * @param {Object} props.columnRatio - Custom column ratio
 * @param {boolean} props.stickyPreview - Whether preview panel should be sticky
 * @param {React.ReactNode} props.children - Additional content
 */
export default function VideoCreationLayout({
  configurationPanel,
  previewPanel,
  header,
  footer,
  className,
  variant = 'default',
  fullWidth = false,
  spacing = 'lg',
  columnRatio = { left: 2, right: 1 },
  stickyPreview = true,
  children,
  ...props
}) {
  const spacingClasses = {
    'sm': 'gap-4',
    'md': 'gap-6',
    'lg': 'gap-8',
    'xl': 'gap-12'
  };

  const ContainerComponent = fullWidth ? 'div' : PageContainer;

  return (
    <div className={cn("min-h-screen bg-background", className)} {...props}>
      {/* Header */}
      {header && (
        <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <ContainerComponent>
            <div className="py-4">
              {header}
            </div>
          </ContainerComponent>
        </div>
      )}

      {/* Main Content */}
      <ContainerComponent className="py-8">
        <div className={cn(
          "grid grid-cols-1 xl:grid-cols-3",
          spacingClasses[spacing]
        )}>
          {/* Configuration Panel - Left Side */}
          <div className={cn(
            "xl:col-span-2",
            variant === 'equal' && "xl:col-span-1"
          )}>
            {configurationPanel}
          </div>

          {/* Preview Panel - Right Side */}
          <div className={cn(
            "xl:col-span-1",
            variant === 'equal' && "xl:col-span-1"
          )}>
            <div className={cn(
              stickyPreview && "sticky top-4 xl:top-20"
            )}>
              {previewPanel}
            </div>
          </div>
        </div>

        {/* Additional Content */}
        {children}
      </ContainerComponent>

      {/* Footer */}
      {footer && (
        <div className="border-t bg-muted/50">
          <ContainerComponent>
            <div className="py-8">
              {footer}
            </div>
          </ContainerComponent>
        </div>
      )}
    </div>
  );
}

/**
 * Specialized layout variants for different use cases
 */

export function StandardVideoCreationLayout(props) {
  return (
    <VideoCreationLayout
      variant="default"
      spacing="lg"
      stickyPreview={true}
      {...props}
    />
  );
}

export function CompactVideoCreationLayout(props) {
  return (
    <VideoCreationLayout
      variant="default"
      spacing="md"
      stickyPreview={false}
      {...props}
    />
  );
}

export function EqualColumnLayout(props) {
  return (
    <VideoCreationLayout
      variant="equal"
      spacing="lg"
      {...props}
    />
  );
}

export function FullWidthVideoCreationLayout(props) {
  return (
    <VideoCreationLayout
      fullWidth={true}
      spacing="xl"
      className="px-4 sm:px-6 lg:px-8"
      {...props}
    />
  );
}

export function SingleColumnLayout({ children, header, footer, className, ...props }) {
  const ContainerComponent = PageContainer;

  return (
    <div className={cn("min-h-screen bg-background", className)} {...props}>
      {/* Header */}
      {header && (
        <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <ContainerComponent>
            <div className="py-4">
              {header}
            </div>
          </ContainerComponent>
        </div>
      )}

      {/* Main Content */}
      <ContainerComponent className="py-8">
        <div className="max-w-4xl mx-auto">
          {children}
        </div>
      </ContainerComponent>

      {/* Footer */}
      {footer && (
        <div className="border-t bg-muted/50">
          <ContainerComponent>
            <div className="py-8">
              {footer}
            </div>
          </ContainerComponent>
        </div>
      )}
    </div>
  );
}

/**
 * Layout with wizard-style steps
 */
export function WizardLayout({ 
  steps = [], 
  currentStep = 0, 
  children, 
  header, 
  footer, 
  className,
  onStepChange,
  ...props 
}) {
  return (
    <div className={cn("min-h-screen bg-background", className)} {...props}>
      {/* Header with Steps */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <PageContainer>
          <div className="py-4 space-y-4">
            {header}
            
            {/* Step Indicator */}
            {steps.length > 0 && (
              <div className="flex items-center justify-center space-x-4">
                {steps.map((step, index) => (
                  <div key={index} className="flex items-center">
                    <button
                      onClick={() => onStepChange?.(index)}
                      className={cn(
                        "flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium transition-colors",
                        index === currentStep
                          ? "bg-primary text-primary-foreground"
                          : index < currentStep
                          ? "bg-primary/20 text-primary"
                          : "bg-muted text-muted-foreground"
                      )}
                    >
                      {index + 1}
                    </button>
                    <span className={cn(
                      "ml-2 text-sm font-medium",
                      index === currentStep ? "text-foreground" : "text-muted-foreground"
                    )}>
                      {step.title}
                    </span>
                    {index < steps.length - 1 && (
                      <div className="w-8 h-px bg-border ml-4" />
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </PageContainer>
      </div>

      {/* Main Content */}
      <PageContainer className="py-8">
        <div className="max-w-4xl mx-auto">
          {children}
        </div>
      </PageContainer>

      {/* Footer */}
      {footer && (
        <div className="border-t bg-muted/50">
          <PageContainer>
            <div className="py-8">
              {footer}
            </div>
          </PageContainer>
        </div>
      )}
    </div>
  );
}
