/**
 * DashboardSidebar Organism
 * Complete navigation sidebar for the dashboard
 * Migrated from dashboard Sidebar component using atomic design principles
 */

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { UserButton } from '@clerk/nextjs';
import { 
  PanelsTopLeft, 
  FileVideo, 
  ShieldPlus, 
  Settings, 
  ChevronDown,
  Calendar,
  Search,
  CreditCard
} from 'lucide-react';
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarHeader,
  SidebarFooter,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from '@/components/ui/sidebar';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { CreditBadge } from '@/components/atoms/indicators';
import { cn } from '@/lib/utils';

/**
 * @param {Object} props
 * @param {number} props.userCredits - Current user credit balance
 * @param {Object} props.user - User object from Clerk
 * @param {string} props.className - Additional CSS classes
 * @param {Array} props.customMenuItems - Custom menu items to override defaults
 * @param {boolean} props.showCredits - Whether to show credit badge
 * @param {React.ReactNode} props.logo - Custom logo component
 * @param {string} props.appName - Application name
 */
export default function DashboardSidebar({
  userCredits,
  user,
  className,
  customMenuItems,
  showCredits = true,
  logo,
  appName = "AI Reel Gen",
  ...props
}) {
  const pathname = usePathname();

  const defaultMenuItems = [
    { 
      id: 1, 
      name: 'Dashboard', 
      icon: PanelsTopLeft, 
      path: '/dashboard' 
    },
    {
      id: 2,
      name: 'Create Video',
      icon: FileVideo,
      path: '/dashboard/create-new-short',
      isCollapsible: true,
      subItems: [
        { id: 2.1, name: 'AI Video', path: '/dashboard/create-new-short/create-ai-video' },
        { id: 2.2, name: 'AI UGC Video', path: '/dashboard/create-new-short/create-ai-ugc-video' },
        { id: 2.3, name: 'Meme Video', path: '/dashboard/create-new-short/create-meme-video' },
        { id: 2.4, name: 'Podcast Clipper', path: '/dashboard/create-new-short/create-podcast-clipper' },
        { id: 2.5, name: 'Reddit Post Video', path: '/dashboard/create-new-short/create-reddit-post-video' },
        { id: 2.6, name: 'Twitter Post Video', path: '/dashboard/create-new-short/create-twitter-post-video' },
        { id: 2.7, name: 'Stock Media Video', path: '/dashboard/create-new-short/create-stock-media-video' },
        { id: 2.8, name: 'Narrator Video', path: '/dashboard/create-new-short/create-new-narrator-short' },
      ]
    },
    { 
      id: 3, 
      name: 'Social Scheduler', 
      icon: Calendar, 
      path: '/dashboard/social-scheduler' 
    },
    { 
      id: 4, 
      name: 'Image Search', 
      icon: Search, 
      path: '/dashboard/image-search' 
    },
    { 
      id: 5, 
      name: 'Billing', 
      icon: CreditCard, 
      path: '/dashboard/billing' 
    },
    { 
      id: 6, 
      name: 'Settings', 
      icon: Settings, 
      path: '/dashboard/settings' 
    },
  ];

  const menuItems = customMenuItems || defaultMenuItems;

  const renderLogo = () => {
    if (logo) return logo;
    
    return (
      <Link href="/dashboard" className="flex items-center font-semibold h-full w-full justify-center">
        <div className="relative h-10 w-10">
          <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
            <FileVideo className="w-5 h-5 text-white" />
          </div>
        </div>
        <span className="truncate text-lg font-bold leading-tight ml-2">{appName}</span>
      </Link>
    );
  };

  const renderMenuItem = (item) => {
    const IconComponent = item.icon;

    if (item.isCollapsible) {
      const isCollapsibleActive = item.subItems.some(subItem => pathname.startsWith(subItem.path));
      
      return (
        <SidebarMenuItem key={item.id}>
          <Collapsible defaultOpen={isCollapsibleActive}>
            <CollapsibleTrigger
              className={cn(
                'flex items-center justify-between w-full gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors',
                isCollapsibleActive
                  ? 'bg-primary text-primary-foreground'
                  : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
              )}
            >
              <div className="flex items-center gap-3">
                <IconComponent className="h-5 w-5" />
                <span>{item.name}</span>
              </div>
              <ChevronDown className="h-4 w-4 transition-transform duration-200 data-[state=open]:rotate-180" />
            </CollapsibleTrigger>
            <CollapsibleContent>
              <SidebarMenuSub>
                {item.subItems.map((subItem) => {
                  const isSubActive = pathname === subItem.path;
                  return (
                    <SidebarMenuSubItem key={subItem.id}>
                      <SidebarMenuSubButton
                        asChild
                        className={cn(
                          isSubActive && 'bg-primary/10 text-primary font-medium'
                        )}
                      >
                        <Link href={subItem.path}>
                          <span>{subItem.name}</span>
                        </Link>
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                  );
                })}
              </SidebarMenuSub>
            </CollapsibleContent>
          </Collapsible>
        </SidebarMenuItem>
      );
    }

    const isActive = pathname === item.path;
    
    return (
      <SidebarMenuItem key={item.id}>
        <Link
          href={item.path}
          className={cn(
            'flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors',
            isActive
              ? 'bg-primary text-primary-foreground'
              : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
          )}
        >
          <IconComponent className="h-5 w-5" />
          <span>{item.name}</span>
        </Link>
      </SidebarMenuItem>
    );
  };

  return (
    <Sidebar className={className} {...props}>
      {/* Header */}
      <SidebarHeader className="bg-background border-b h-16 min-h-[64px] flex items-center justify-center">
        {renderLogo()}
      </SidebarHeader>

      {/* Navigation Content */}
      <SidebarContent>
        <SidebarGroup>
          <SidebarMenu>
            {menuItems.map(renderMenuItem)}
          </SidebarMenu>
        </SidebarGroup>
      </SidebarContent>

      {/* Footer */}
      <SidebarFooter className="border-t bg-background/50">
        <div className="p-4 space-y-4">
          {/* Credits Display */}
          {showCredits && userCredits !== undefined && (
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Credits</span>
              <CreditBadge 
                credits={userCredits} 
                showIcon={true}
                size="sm"
              />
            </div>
          )}

          {/* User Profile */}
          <div className="flex items-center gap-3">
            <UserButton 
              appearance={{
                elements: {
                  avatarBox: "w-8 h-8"
                }
              }}
            />
            {user && (
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-foreground truncate">
                  {user.firstName || user.username || 'User'}
                </p>
                <p className="text-xs text-muted-foreground truncate">
                  {user.emailAddresses?.[0]?.emailAddress}
                </p>
              </div>
            )}
          </div>
        </div>
      </SidebarFooter>
    </Sidebar>
  );
}

/**
 * Specialized sidebar variants for different contexts
 */

export function CompactDashboardSidebar(props) {
  const compactMenuItems = [
    { id: 1, name: 'Dashboard', icon: PanelsTopLeft, path: '/dashboard' },
    { id: 2, name: 'Create', icon: FileVideo, path: '/dashboard/create-new-short' },
    { id: 3, name: 'Billing', icon: CreditCard, path: '/dashboard/billing' },
  ];

  return (
    <DashboardSidebar
      customMenuItems={compactMenuItems}
      showCredits={false}
      {...props}
    />
  );
}

export function AdminDashboardSidebar(props) {
  const adminMenuItems = [
    { id: 1, name: 'Dashboard', icon: PanelsTopLeft, path: '/admin/dashboard' },
    { id: 2, name: 'Users', icon: Settings, path: '/admin/users' },
    { id: 3, name: 'Analytics', icon: Search, path: '/admin/analytics' },
    { id: 4, name: 'Settings', icon: Settings, path: '/admin/settings' },
  ];

  return (
    <DashboardSidebar
      customMenuItems={adminMenuItems}
      appName="Admin Panel"
      {...props}
    />
  );
}
