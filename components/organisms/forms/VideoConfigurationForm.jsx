/**
 * VideoConfigurationForm Organism
 * Complete left-side configuration form that orchestrates multiple form molecules
 * Used across all video creation workflows with different section combinations
 */

import React from 'react';
import { 
  ProjectDetailsForm, 
  ScriptInputForm, 
  MediaUploadForm 
} from '@/components/molecules/forms';
import { AspectRatioSelector } from '@/components/molecules/selection';
import { Container, SectionSpacer } from '@/components/atoms/layout';
import { ErrorMessage } from '@/components/atoms/display';
import { cn } from '@/lib/utils';

/**
 * @param {Object} props
 * @param {string} props.videoType - Type of video being configured
 * @param {Object} props.formData - Current form data
 * @param {Object} props.errors - Form validation errors
 * @param {Function} props.onFieldChange - Field change handler
 * @param {Function} props.onFieldsChange - Multiple fields change handler
 * @param {Array} props.sections - Sections to include in the form
 * @param {Object} props.sectionProps - Props for specific sections
 * @param {string} props.className - Additional CSS classes
 * @param {React.ReactNode} props.children - Additional form sections
 * @param {React.ReactNode} props.headerContent - Content above form sections
 * @param {React.ReactNode} props.footerContent - Content below form sections
 */
export default function VideoConfigurationForm({
  videoType = 'default',
  formData = {},
  errors = {},
  onFieldChange,
  onFieldsChange,
  sections = ['project', 'script', 'media', 'aspectRatio'],
  sectionProps = {},
  className,
  children,
  headerContent,
  footerContent,
  ...props
}) {
  const handleFieldChange = (field, value) => {
    onFieldChange?.(field, value);
  };

  const handleProjectTitleChange = (value) => {
    handleFieldChange('projectTitle', value);
  };

  const handleProjectDescriptionChange = (value) => {
    handleFieldChange('projectDescription', value);
  };

  const handleScriptModeChange = (mode) => {
    handleFieldChange('scriptMode', mode);
  };

  const handleAIPromptChange = (value) => {
    handleFieldChange('aiPrompt', value);
  };

  const handleManualScriptChange = (value) => {
    handleFieldChange('manualScript', value);
  };

  const handleAspectRatioChange = (value) => {
    handleFieldChange('aspectRatio', value);
  };

  const handleFileUpload = (files) => {
    const currentFiles = formData.uploadedFiles || [];
    const newFiles = files.map(file => ({
      name: file.name,
      size: file.size,
      type: file.type,
      file: file
    }));
    handleFieldChange('uploadedFiles', [...currentFiles, ...newFiles]);
  };

  const handleFileRemove = (index) => {
    const currentFiles = formData.uploadedFiles || [];
    const updatedFiles = currentFiles.filter((_, i) => i !== index);
    handleFieldChange('uploadedFiles', updatedFiles);
  };

  const renderSection = (sectionType) => {
    const props = sectionProps[sectionType] || {};

    switch (sectionType) {
      case 'project':
        return (
          <ProjectDetailsForm
            projectTitle={formData.projectTitle || ''}
            projectDescription={formData.projectDescription || ''}
            onProjectTitleChange={handleProjectTitleChange}
            onProjectDescriptionChange={handleProjectDescriptionChange}
            errors={errors}
            {...props}
          />
        );

      case 'script':
        return (
          <ScriptInputForm
            scriptMode={formData.scriptMode || 'ai'}
            aiPrompt={formData.aiPrompt || ''}
            manualScript={formData.manualScript || ''}
            generatedScript={formData.generatedScript || ''}
            onScriptModeChange={handleScriptModeChange}
            onAIPromptChange={handleAIPromptChange}
            onManualScriptChange={handleManualScriptChange}
            onGenerateScript={props.onGenerateScript}
            isGeneratingScript={formData.isGeneratingScript || false}
            errors={errors}
            {...props}
          />
        );

      case 'media':
        return (
          <MediaUploadForm
            uploadedFiles={formData.uploadedFiles || []}
            uploadingFiles={formData.uploadingFiles || []}
            onFileUpload={handleFileUpload}
            onRemoveFile={handleFileRemove}
            {...props}
          />
        );

      case 'aspectRatio':
        return (
          <AspectRatioSelector
            value={formData.aspectRatio || '16:9'}
            onChange={handleAspectRatioChange}
            {...props}
          />
        );

      default:
        return null;
    }
  };

  return (
    <div className={cn("space-y-8", className)} {...props}>
      {/* Header Content */}
      {headerContent}

      {/* Form Sections */}
      <div className="space-y-8">
        {sections.map((section, index) => (
          <div key={section}>
            {renderSection(section)}
            {index < sections.length - 1 && <SectionSpacer size="lg" />}
          </div>
        ))}

        {/* Additional Custom Sections */}
        {children}
      </div>

      {/* Footer Content */}
      {footerContent}

      {/* Global Form Errors */}
      {errors.form && (
        <ErrorMessage
          message={errors.form}
          variant="error"
          size="md"
        />
      )}
    </div>
  );
}

/**
 * Specialized video configuration form variants for different video types
 */

export function AIVideoConfigurationForm(props) {
  return (
    <VideoConfigurationForm
      videoType="ai-video"
      sections={['project', 'script', 'aspectRatio']}
      sectionProps={{
        project: {
          title: "AI Video Project",
          description: "Set up your AI-generated video project"
        },
        script: {
          title: "Video Script",
          description: "Create your video script using AI or write it manually"
        },
        aspectRatio: {
          title: "Video Format",
          description: "Choose the aspect ratio for your AI video"
        }
      }}
      {...props}
    />
  );
}

export function MemeVideoConfigurationForm(props) {
  return (
    <VideoConfigurationForm
      videoType="meme-video"
      sections={['project', 'media', 'aspectRatio']}
      sectionProps={{
        project: {
          title: "Meme Video Project",
          description: "Set up your meme video project",
          showDescription: false
        },
        media: {
          title: "Upload Video",
          description: "Upload the video you want to turn into a meme",
          acceptedTypes: ['video/*'],
          multiple: false,
          maxFiles: 1
        },
        aspectRatio: {
          title: "Meme Format",
          description: "Choose the format for your meme video",
          size: "sm"
        }
      }}
      {...props}
    />
  );
}

export function PodcastClipperConfigurationForm(props) {
  return (
    <VideoConfigurationForm
      videoType="podcast-clipper"
      sections={['project', 'media']}
      sectionProps={{
        project: {
          title: "Podcast Clipper Project",
          description: "Set up your podcast clipping project"
        },
        media: {
          title: "Upload Podcast",
          description: "Upload your podcast audio file for clipping",
          acceptedTypes: ['audio/*', 'video/*'],
          multiple: false,
          maxFiles: 1
        }
      }}
      {...props}
    />
  );
}

export function AIUGCConfigurationForm(props) {
  return (
    <VideoConfigurationForm
      videoType="ai-ugc-video"
      sections={['project', 'script', 'media']}
      sectionProps={{
        project: {
          title: "AI UGC Video Project",
          description: "Set up your AI UGC video project"
        },
        script: {
          title: "UGC Script",
          description: "Create the script that your AI creator will speak"
        },
        media: {
          title: "Background Assets",
          description: "Upload background images for your UGC video",
          acceptedTypes: ['image/*'],
          multiple: true,
          maxFiles: 10
        }
      }}
      {...props}
    />
  );
}

export function CustomConfigurationForm({ customSections = [], ...props }) {
  return (
    <VideoConfigurationForm
      sections={customSections}
      {...props}
    />
  );
}
