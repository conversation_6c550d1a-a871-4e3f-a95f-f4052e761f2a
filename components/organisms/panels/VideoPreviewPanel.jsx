/**
 * VideoPreviewPanel Organism
 * Complete right-side preview panel used across all video creation workflows
 * Composes VideoPreviewCard, ConfigurationSummary, and GenerationButton
 */

import React from 'react';
import { VideoPreviewCard, ConfigurationSummary } from '@/components/molecules/preview';
import { GenerationButton } from '@/components/atoms/buttons';
import { Container } from '@/components/atoms/layout';
import { ErrorMessage } from '@/components/atoms/display';
import { cn } from '@/lib/utils';

/**
 * @param {Object} props
 * @param {string} props.videoType - Type of video being created
 * @param {Object} props.configuration - Current video configuration
 * @param {string} props.previewTitle - Preview card title
 * @param {string} props.videoUrl - Video URL for preview
 * @param {string} props.thumbnailUrl - Thumbnail URL
 * @param {string} props.status - Generation status
 * @param {string} props.duration - Video duration
 * @param {string} props.aspectRatio - Video aspect ratio
 * @param {number} props.credits - User credits
 * @param {number} props.requiredCredits - Credits required
 * @param {boolean} props.isGenerating - Whether generation is in progress
 * @param {Function} props.onGenerate - Generation handler
 * @param {boolean} props.canGenerate - Whether generation is allowed
 * @param {string} props.generationError - Generation error message
 * @param {string} props.className - Additional CSS classes
 * @param {boolean} props.showConfiguration - Whether to show config summary
 * @param {boolean} props.sticky - Whether panel should be sticky
 * @param {React.ReactNode} props.additionalActions - Additional action buttons
 * @param {React.ReactNode} props.children - Additional content
 */
export default function VideoPreviewPanel({
  videoType = 'default',
  configuration = {},
  previewTitle,
  videoUrl,
  thumbnailUrl,
  status = 'pending',
  duration,
  aspectRatio = '16:9',
  credits,
  requiredCredits,
  isGenerating = false,
  onGenerate,
  canGenerate = true,
  generationError,
  className,
  showConfiguration = true,
  sticky = true,
  additionalActions,
  children,
  ...props
}) {
  // Determine preview title based on video type
  const getPreviewTitle = () => {
    if (previewTitle) return previewTitle;
    
    const titleMap = {
      'ai-video': 'AI Video Preview',
      'meme-video': 'Meme Video Preview',
      'podcast-clipper': 'Podcast Clips Preview',
      'ai-ugc-video': 'AI UGC Video Preview'
    };
    
    return titleMap[videoType] || 'Video Preview';
  };

  // Determine required credits based on video type
  const getRequiredCredits = () => {
    if (requiredCredits !== undefined) return requiredCredits;
    
    const creditMap = {
      'ai-video': 10,
      'meme-video': 5,
      'podcast-clipper': 8,
      'ai-ugc-video': 15
    };
    
    return creditMap[videoType] || 10;
  };

  const finalRequiredCredits = getRequiredCredits();
  const hasSufficientCredits = credits === undefined || credits >= finalRequiredCredits;
  const isGenerateDisabled = !canGenerate || isGenerating || !hasSufficientCredits;

  const getGenerationButtonText = () => {
    if (isGenerating) {
      const messageMap = {
        'ai-video': 'Creating AI Video...',
        'meme-video': 'Creating Meme Video...',
        'podcast-clipper': 'Processing Podcast...',
        'ai-ugc-video': 'Creating UGC Video...'
      };
      return messageMap[videoType] || 'Generating...';
    }
    
    const textMap = {
      'ai-video': 'Generate AI Video',
      'meme-video': 'Generate Meme Video',
      'podcast-clipper': 'Clip Podcast',
      'ai-ugc-video': 'Generate UGC Video'
    };
    
    return textMap[videoType] || 'Generate Video';
  };

  return (
    <div 
      className={cn(
        "space-y-6",
        sticky && "sticky top-4 xl:top-20 h-fit",
        className
      )} 
      {...props}
    >
      {/* Background decoration */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-secondary/5 rounded-2xl -z-10"></div>
      
      <Container
        variant="elevated"
        className="relative bg-background/80 backdrop-blur-sm border border-border/50 max-h-[calc(100vh-2rem)] xl:max-h-[calc(100vh-6rem)] overflow-y-auto"
      >
        <div className="space-y-6">
          {/* Video Preview Card */}
          <VideoPreviewCard
            title={getPreviewTitle()}
            videoUrl={videoUrl}
            thumbnailUrl={thumbnailUrl}
            status={status}
            duration={duration}
            aspectRatio={aspectRatio}
            credits={credits}
            requiredCredits={finalRequiredCredits}
            showCredits={true}
            showStatus={true}
            isLoading={isGenerating}
          />

          {/* Configuration Summary */}
          {showConfiguration && Object.keys(configuration).length > 0 && (
            <ConfigurationSummary
              configuration={configuration}
              title="Current Settings"
              size="sm"
            />
          )}

          {/* Additional Content */}
          {children}

          {/* Generation Error */}
          {generationError && (
            <ErrorMessage
              message={generationError}
              variant="error"
              size="sm"
            />
          )}

          {/* Generation Actions */}
          <div className="space-y-3">
            {/* Additional Actions */}
            {additionalActions && (
              <div className="flex flex-wrap gap-2">
                {additionalActions}
              </div>
            )}

            {/* Main Generation Button */}
            <div className="pt-4 border-t border-border/30">
              <GenerationButton
                videoType={videoType}
                isGenerating={isGenerating}
                disabled={isGenerateDisabled}
                onClick={onGenerate}
                credits={credits}
                requiredCredits={finalRequiredCredits}
                defaultText={getGenerationButtonText()}
                generationMessage={getGenerationButtonText()}
                className="w-full"
                size="lg"
              />
            </div>

            {/* Credit Information */}
            {!hasSufficientCredits && (
              <div className="text-center text-sm text-muted-foreground">
                <p>Need {finalRequiredCredits} credits • You have {credits || 0}</p>
                <p className="text-xs mt-1">
                  <a href="/pricing" className="text-primary hover:underline">
                    Get more credits
                  </a>
                </p>
              </div>
            )}
          </div>
        </div>
      </Container>
    </div>
  );
}

/**
 * Specialized video preview panel variants for different video types
 */

export function AIVideoPreviewPanel(props) {
  return (
    <VideoPreviewPanel
      videoType="ai-video"
      previewTitle="AI Video Preview"
      {...props}
    />
  );
}

export function MemeVideoPreviewPanel(props) {
  return (
    <VideoPreviewPanel
      videoType="meme-video"
      previewTitle="Meme Video Preview"
      {...props}
    />
  );
}

export function PodcastClipperPreviewPanel(props) {
  return (
    <VideoPreviewPanel
      videoType="podcast-clipper"
      previewTitle="Podcast Clips Preview"
      {...props}
    />
  );
}

export function AIUGCPreviewPanel(props) {
  return (
    <VideoPreviewPanel
      videoType="ai-ugc-video"
      previewTitle="AI UGC Video Preview"
      {...props}
    />
  );
}

export function CompactPreviewPanel(props) {
  return (
    <VideoPreviewPanel
      showConfiguration={false}
      sticky={false}
      {...props}
    />
  );
}
