/**
 * VideoLibraryGrid Organism
 * Complete video library display with grid/list views, pagination, and video player
 * Migrated from dashboard VideoList component using atomic design principles
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { StatusBadge } from '@/components/atoms/indicators';
import { LoadingSpinner, PageSpinner } from '@/components/atoms/display';
import { ErrorMessage } from '@/components/atoms/display';
import { SectionHeader } from '@/components/atoms/layout';
import Image from 'next/image';
import { Play, RefreshCw, Grid, List, Eye } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { cn } from '@/lib/utils';

/**
 * @param {Object} props
 * @param {Array} props.videos - Array of video objects
 * @param {boolean} props.loading - Whether videos are loading
 * @param {string} props.error - Error message if any
 * @param {boolean} props.isRefreshing - Whether refresh is in progress
 * @param {Function} props.onRefresh - Refresh handler
 * @param {Function} props.onVideoClick - Video click handler
 * @param {Object} props.pagination - Pagination data
 * @param {Function} props.onPageChange - Page change handler
 * @param {string} props.viewMode - Display mode ('grid' or 'list')
 * @param {Function} props.onViewModeChange - View mode change handler
 * @param {string} props.className - Additional CSS classes
 * @param {string} props.title - Section title
 * @param {boolean} props.showHeader - Whether to show section header
 * @param {boolean} props.showViewToggle - Whether to show view mode toggle
 * @param {boolean} props.showRefresh - Whether to show refresh button
 * @param {React.ReactNode} props.emptyState - Custom empty state component
 */
export default function VideoLibraryGrid({
  videos = [],
  loading = false,
  error = null,
  isRefreshing = false,
  onRefresh,
  onVideoClick,
  pagination = { currentPage: 1, totalPages: 1, totalCount: 0 },
  onPageChange,
  viewMode = 'grid',
  onViewModeChange,
  className,
  title = "Video Library",
  showHeader = true,
  showViewToggle = true,
  showRefresh = true,
  emptyState,
  ...props
}) {
  const { currentPage, totalPages, totalCount } = pagination;

  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= totalPages && newPage !== currentPage) {
      onPageChange?.(newPage);
    }
  };

  const getStatusVariant = (status) => {
    const statusMap = {
      'Completed': 'completed',
      'Processing': 'processing',
      'Pending': 'pending',
      'Failed': 'failed'
    };
    return statusMap[status] || 'pending';
  };

  const renderVideoCard = (video) => (
    <Card
      key={video.id}
      className={cn(
        "overflow-hidden cursor-pointer group transition-all duration-200",
        "hover:shadow-lg hover:scale-[1.02]",
        video.status !== 'Completed' && "cursor-not-allowed opacity-75"
      )}
      onClick={() => video.status === 'Completed' ? onVideoClick?.(video.id) : null}
    >
      <CardContent className="p-0">
        <div className="relative aspect-[9/16] w-full bg-gradient-to-br from-muted to-muted/50">
          {/* Video Thumbnail */}
          {video.status === 'Completed' && video.images && video.images.length > 0 ? (
            <Image
              src={video.images[0]}
              alt={`Thumbnail for ${video.title || 'video'}`}
              fill
              sizes="(max-width: 640px) 100vw, 280px"
              style={{ objectFit: 'cover' }}
              className="transition-transform duration-300 group-hover:scale-105"
              unoptimized
            />
          ) : video.status === 'Completed' ? (
            <div className="flex items-center justify-center w-full h-full bg-gradient-to-br from-muted to-muted/80 text-muted-foreground">
              <div className="text-center">
                <Eye className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <span className="text-sm">No Thumbnail</span>
              </div>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center w-full h-full bg-gradient-to-br from-muted to-muted/80 text-muted-foreground">
              {video.status === 'Processing' || video.status === 'Pending' ? (
                <>
                  <LoadingSpinner size="lg" className="mb-3 text-primary" />
                  <div className="text-center">
                    <div className="text-sm font-medium capitalize mb-1">{video.status}</div>
                    <div className="text-xs">Please wait...</div>
                  </div>
                </>
              ) : video.status === 'Failed' ? (
                <div className="text-center">
                  <div className="text-2xl mb-2">❌</div>
                  <div className="text-sm font-medium text-destructive mb-1">Failed</div>
                  <div className="text-xs">Try again</div>
                </div>
              ) : null}
            </div>
          )}

          {/* Play Overlay */}
          {video.status === 'Completed' && (
            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-all duration-300 flex items-center justify-center">
              <div className="transform scale-75 group-hover:scale-100 transition-transform duration-300">
                <Play className="h-12 w-12 text-white opacity-0 group-hover:opacity-90 transition-opacity duration-300" />
              </div>
            </div>
          )}
        </div>

        {/* Video Info */}
        <div className="p-4">
          <h3 className="truncate text-base font-semibold mb-2">
            {video.title || 'Untitled Video'}
          </h3>
          <div className="flex items-center justify-between">
            {video.createdAt && (
              <p className="text-sm text-muted-foreground">
                {formatDistanceToNow(new Date(video.createdAt), { addSuffix: true })}
              </p>
            )}
            <StatusBadge 
              status={getStatusVariant(video.status)} 
              size="sm" 
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const renderVideoListItem = (video) => (
    <Card
      key={video.id}
      className={cn(
        "cursor-pointer group transition-all duration-200 hover:shadow-md",
        video.status !== 'Completed' && "cursor-not-allowed opacity-75"
      )}
      onClick={() => video.status === 'Completed' ? onVideoClick?.(video.id) : null}
    >
      <CardContent className="p-4">
        <div className="flex items-center gap-4">
          {/* Thumbnail */}
          <div className="relative w-16 h-16 bg-muted rounded-lg overflow-hidden flex-shrink-0">
            {video.status === 'Completed' && video.images && video.images.length > 0 ? (
              <Image
                src={video.images[0]}
                alt={`Thumbnail for ${video.title || 'video'}`}
                fill
                style={{ objectFit: 'cover' }}
                unoptimized
              />
            ) : (
              <div className="flex items-center justify-center w-full h-full">
                <Eye className="h-6 w-6 text-muted-foreground opacity-50" />
              </div>
            )}
          </div>

          {/* Video Info */}
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold truncate mb-1">
              {video.title || 'Untitled Video'}
            </h3>
            <p className="text-sm text-muted-foreground">
              {video.createdAt && formatDistanceToNow(new Date(video.createdAt), { addSuffix: true })}
            </p>
          </div>

          {/* Status */}
          <StatusBadge 
            status={getStatusVariant(video.status)} 
            size="sm" 
          />
        </div>
      </CardContent>
    </Card>
  );

  // Loading state
  if (loading && videos.length === 0) {
    return (
      <div className={cn("space-y-6", className)} {...props}>
        {showHeader && (
          <SectionHeader title={title} description="Loading your video library..." />
        )}
        <PageSpinner message="Loading videos..." />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={cn("space-y-6", className)} {...props}>
        {showHeader && (
          <SectionHeader title={title} description="Error loading videos" />
        )}
        <ErrorMessage
          title="Loading Error"
          message={error}
          variant="error"
          retry={onRefresh}
        />
      </div>
    );
  }

  // Empty state
  if (!loading && totalCount === 0 && !error) {
    return (
      <div className={cn("space-y-6", className)} {...props}>
        {showHeader && (
          <SectionHeader title={title} description="No videos found" />
        )}
        {emptyState || (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🎬</div>
            <h3 className="text-lg font-semibold mb-2">No videos yet</h3>
            <p className="text-muted-foreground mb-4">
              Create your first video to get started
            </p>
            <Button asChild>
              <a href="/dashboard/create-new-short">Create Video</a>
            </Button>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)} {...props}>
      {/* Header with Controls */}
      <div className="flex justify-between items-center">
        <div>
          {showHeader && (
            <SectionHeader 
              title={title} 
              description={`${totalCount} video${totalCount === 1 ? '' : 's'} • Page ${currentPage} of ${totalPages}`}
              showHeader={false}
            />
          )}
        </div>

        <div className="flex items-center gap-2">
          {/* View Mode Toggle */}
          {showViewToggle && (
            <div className="flex items-center gap-1 bg-muted rounded-lg p-1">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => onViewModeChange?.('grid')}
                className="h-8 w-8 p-0"
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => onViewModeChange?.('list')}
                className="h-8 w-8 p-0"
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          )}

          {/* Refresh Button */}
          {showRefresh && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRefresh}
              disabled={isRefreshing}
            >
              <RefreshCw className={cn("h-4 w-4 mr-2", isRefreshing && "animate-spin")} />
              {isRefreshing ? 'Refreshing...' : 'Refresh'}
            </Button>
          )}
        </div>
      </div>

      {/* Loading Overlay */}
      {loading && videos.length > 0 && (
        <div className="relative">
          <div className="absolute inset-0 bg-background/50 flex justify-center items-center z-10 rounded-lg">
            <LoadingSpinner size="md" />
          </div>
        </div>
      )}

      {/* Video Grid/List */}
      <div className={cn(
        viewMode === 'grid' 
          ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
          : "space-y-4"
      )}>
        {videos.map((video) => 
          viewMode === 'grid' ? renderVideoCard(video) : renderVideoListItem(video)
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  href="#"
                  onClick={(e) => { e.preventDefault(); handlePageChange(currentPage - 1); }}
                  className={currentPage <= 1 ? 'pointer-events-none opacity-50' : undefined}
                />
              </PaginationItem>

              {[...Array(totalPages)].map((_, i) => (
                <PaginationItem key={i + 1}>
                  <PaginationLink
                    href="#"
                    onClick={(e) => { e.preventDefault(); handlePageChange(i + 1); }}
                    isActive={currentPage === i + 1}
                  >
                    {i + 1}
                  </PaginationLink>
                </PaginationItem>
              ))}

              <PaginationItem>
                <PaginationNext
                  href="#"
                  onClick={(e) => { e.preventDefault(); handlePageChange(currentPage + 1); }}
                  className={currentPage >= totalPages ? 'pointer-events-none opacity-50' : undefined}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}
    </div>
  );
}

/**
 * Specialized video library variants for different contexts
 */

export function CompactVideoLibrary(props) {
  return (
    <VideoLibraryGrid
      showHeader={false}
      showViewToggle={false}
      showRefresh={false}
      viewMode="grid"
      {...props}
    />
  );
}

export function DashboardVideoLibrary(props) {
  return (
    <VideoLibraryGrid
      title="Your Video Library"
      showHeader={true}
      showViewToggle={true}
      showRefresh={true}
      {...props}
    />
  );
}
